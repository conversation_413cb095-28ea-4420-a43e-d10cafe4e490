package com.hengjian.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;

/**
 * 响应体-租户导出
 *
 * <AUTHOR>
 * @date 2023/9/27
 */
@Data
public class SysTenantExportVo {

    @ExcelProperty(order = 0)
    @ExcelI18nFormat(code = "zsmall.excel.nickName")
    private String nickName;

    @ExcelProperty(order = 10)
    @ExcelI18nFormat(code = "zsmall.excel.tenantId")
    private String tenantId;

    @ExcelProperty(order = 20)
    @ExcelI18nFormat(code = "zsmall.excel.thirdChannelFlag")
    private String thirdChannelFlag;

    @ExcelProperty(order = 30)
    @ExcelI18nFormat(code = "zsmall.excel.tenantType")
    private String tenantType;

    @ExcelProperty(order = 40)
    @ExcelI18nFormat(code = "zsmall.excel.email")
    private String email;

    @ExcelProperty(order = 50)
    @ExcelI18nFormat(code = "zsmall.excel.phoneNumber")
    private String phoneNumber;

    @ExcelProperty(order = 60)
    @ExcelI18nFormat(code = "zsmall.excel.createTime")
    private String createTime;

    @ExcelProperty(order = 70)
    @ExcelI18nFormat(code = "zsmall.excel.loginDate")
    private String loginDate;

    @ExcelProperty(order = 80)
    @ExcelI18nFormat(code = "zsmall.excel.tenantStatus")
    private String status;

}
