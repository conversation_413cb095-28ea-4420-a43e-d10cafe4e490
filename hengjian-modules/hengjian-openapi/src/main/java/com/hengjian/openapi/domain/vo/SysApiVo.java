package com.hengjian.openapi.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.openapi.domain.SysApi;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 应用配置视图对象 sys_api
 *
 * <AUTHOR> Li
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysApi.class)
public class SysApiVo implements Serializable {


    private static final long serialVersionUID = 1872510571025L;

    /**
     * 应用ID
     */
    @ExcelProperty(value = "应用ID")
    private Long apiId;

    /**
     * 租户ID
     */
    @ExcelProperty(value = "租户ID")
    private String  tenantId;

    /**
     * 应用Key
     */
    @ExcelProperty(value = "应用Key")
    private String apiKey;

    /**
     * 应用密钥
     */
    @ExcelProperty(value = "应用密钥")
    private String apiSecretkey;

    /**
     * 应用名称
     */
    @ExcelProperty(value = "应用名称")
    private String apiName;


}
