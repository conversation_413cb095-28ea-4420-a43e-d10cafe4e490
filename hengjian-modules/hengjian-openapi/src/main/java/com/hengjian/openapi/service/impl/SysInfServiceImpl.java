package com.hengjian.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.openapi.domain.SysApi;
import com.hengjian.openapi.domain.SysApiInf;
import com.hengjian.openapi.domain.SysInf;
import com.hengjian.openapi.domain.bo.SysInfBo;
import com.hengjian.openapi.domain.vo.SysInfVo;
import com.hengjian.openapi.mapper.SysApiInfMapper;
import com.hengjian.openapi.mapper.SysApiMapper;
import com.hengjian.openapi.mapper.SysInfMapper;
import com.hengjian.openapi.service.ISysInfService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 应用接口Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-05-13
 */
@RequiredArgsConstructor
@Service
public class SysInfServiceImpl implements ISysInfService {

    private final SysInfMapper baseMapper;
    private final SysApiMapper apiMapper;
    private final SysApiInfMapper sysApiInfMapper;

    /**
     * 查询应用接口
     */
    @Override
    public SysInfVo queryById(Long apiId){
        return baseMapper.selectVoById(apiId);
    }

    /**
     * 查询应用接口列表
     */
    @Override
    public TableDataInfo<SysInfVo> queryPageList(SysInfBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysInf> lqw = buildQueryWrapper(bo);
        Page<SysInfVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询应用接口列表
     */
    @Override
    public List<SysInfVo> queryList(SysInfBo bo) {
        LambdaQueryWrapper<SysInf> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysInf> buildQueryWrapper(SysInfBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysInf> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getInfName()), SysInf::getInfName, bo.getInfName());
        lqw.eq(bo.getInfType() != null, SysInf::getInfType, bo.getInfType());
        lqw.eq(StringUtils.isNotBlank(bo.getInfUrl()), SysInf::getInfUrl, bo.getInfUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getInfTestUrl()), SysInf::getInfTestUrl, bo.getInfTestUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getInfParameters()), SysInf::getInfParameters, bo.getInfParameters());
        lqw.eq(StringUtils.isNotBlank(bo.getInfNote()), SysInf::getInfNote, bo.getInfNote());
        lqw.eq(bo.getRequestType() != null, SysInf::getRequestType, bo.getRequestType());
        lqw.eq(bo.getIsTest() != null, SysInf::getIsTest, bo.getIsTest());
        return lqw;
    }

    /**
     * 新增应用接口
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SysInfBo bo) {
        //校验应用ID是否存在
        SysApi sysApi = apiMapper.selectById(bo.getApiId());
        if (Objects.isNull(sysApi)){
            throw new RuntimeException("应用ID{}不存在"+bo.getApiId());
        }

        SysInf add = MapstructUtils.convert(bo, SysInf.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        //插入关联表
        if (flag) {
            SysApiInf sysApiInf = new SysApiInf();
            sysApiInf.setSysInfId(add.getInfId());
            sysApiInf.setSysApiId(bo.getApiId());
            sysApiInfMapper.insert(sysApiInf);
        }
        return flag;
    }

    /**
     * 修改应用接口
     */
    @Override
    public Boolean updateByBo(SysInfBo bo) {
        SysInf update = MapstructUtils.convert(bo, SysInf.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysInf entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除应用接口
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids,Long apiId, Boolean isValid) {
        SysApi sysApi = apiMapper.selectById(apiId);
        if (Objects.isNull(sysApi)){
            throw new RuntimeException("应用ID:"+apiId+" 不存在");
        }
        baseMapper.deleteBatchIds(ids);
         //删除关联关系
        LambdaQueryWrapper<SysApiInf> lqw = Wrappers.lambdaQuery();
        lqw.in(SysApiInf::getSysInfId, ids);
        lqw.eq(SysApiInf::getSysApiId, apiId);
        return  sysApiInfMapper.delete(lqw) >0;
    }

    @Override
    public SysInfVo queryByInfNote(String infNote) {
        LambdaQueryWrapper<SysInf> infVoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        infVoLambdaQueryWrapper.eq(SysInf::getInfNote, infNote);
        infVoLambdaQueryWrapper.eq(SysInf::getDelFlag, 0);
        infVoLambdaQueryWrapper.eq(SysInf::getInfType, 3);
        return baseMapper.selectVoOne(infVoLambdaQueryWrapper);
    }
}
