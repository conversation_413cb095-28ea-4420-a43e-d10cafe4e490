package com.hengjian.extend.event;

import cn.hutool.core.io.IoUtil;
import com.hengjian.common.core.utils.SpringUtils;
import lombok.Getter;
import lombok.Setter;

import java.io.InputStream;

/**
 * 事件 - OSS文件获取
 *
 * <AUTHOR>
 * @date 2023/6/13
 */
@Setter
@Getter
public class OSSObtainEvent {

    private Long ossId;

    private String url;

    private byte[] bytes;

    public static InputStream obtainFileInputStream(String url) {
        OSSObtainEvent event = new OSSObtainEvent(url);
        SpringUtils.context().publishEvent(event);
        return IoUtil.toStream(event.getBytes());
    }

    public static InputStream obtainFileInputStream(Long ossId) {
        OSSObtainEvent event = new OSSObtainEvent(ossId);
        SpringUtils.context().publishEvent(event);
        return IoUtil.toStream(event.getBytes());
    }

    public static byte[] obtainFileBytes(Long ossId) {
        OSSObtainEvent event = new OSSObtainEvent(ossId);
        SpringUtils.context().publishEvent(event);
        return event.getBytes();
    }

    public OSSObtainEvent(String url) {
        this.url = url;
    }

    public OSSObtainEvent(Long ossId) {
        this.ossId = ossId;
    }

}
