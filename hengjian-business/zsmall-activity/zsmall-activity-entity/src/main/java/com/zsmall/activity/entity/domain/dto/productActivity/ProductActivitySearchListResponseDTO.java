package com.zsmall.activity.entity.domain.dto.productActivity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ProductActivitySearchListResponseDTO {
    /**
     * 商品图片
     */
    private String productImg;
    /**
     * 商品Sku唯一编码
     */
    private String productSkuCode;
    /**
     * 商品名称
     */
    private String productName;

    /**
     * 活动编号
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 站点
     */
    private String site;

    /**
     * 币种
     */
    private String currencySymbol;

    /**
     * 租户编号（供货商）
     */
    private String supplierTenantId;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    private String activityState;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 活动开始时间(分销商)
     */
    private Date activeStartTime;

    /**
     * 活动结束时间(分销商)
     */
    private Date activeEndTime;
    /**
     * 剩余时间
     */
    private Long remainingDay;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 活动自提锁货库存总数
     */
    private Long pickupQuantityLocked;

    /**
     * 活动代发锁货库存总数
     */
    private Long dropShippingQuantityLocked;
    /**
     * 活动自提锁货库存已使用数量
     */
    private Long pickupLockedUsed;
    /**
     * 活动代发锁货库存已使用数量
     */
    private Long dropShippingLockedUsed;

}
