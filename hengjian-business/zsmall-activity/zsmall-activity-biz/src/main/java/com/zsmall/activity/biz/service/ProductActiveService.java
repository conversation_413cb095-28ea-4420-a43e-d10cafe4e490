package com.zsmall.activity.biz.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.core.utils.trans.TransactionUtils;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.SysInfEnum;
import com.hengjian.openapi.service.ISysInfService;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.zsmall.activity.biz.listener.SupperProductActiveImportListener;
import com.zsmall.activity.biz.support.ProductActiveSupper;
import com.zsmall.activity.entity.domain.dto.productActivity.*;
import com.zsmall.activity.entity.domain.dto.productActivity.erp.ErpProductLockInventoryReleaseRequest;
import com.zsmall.activity.entity.domain.dto.productActivity.erp.ErpProductLockInventoryReservedRequest;
import com.zsmall.activity.entity.domain.dto.productActivity.export.*;
import com.zsmall.activity.entity.iservice.*;
import com.zsmall.activity.entity.mapper.DistributorProductActivityMapper;
import com.zsmall.activity.entity.mapper.SupplierProductActivityMapper;
import com.zsmall.activity.entity.util.ProductActiveCodeUtil;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productActivity.ProductActivityExceptionEnum;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.extend.es.esmapper.EsProductMapper;
import com.zsmall.extend.wms.exception.ThebizarkException;
import com.zsmall.extend.wms.model.stock.OutStock;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductActiveService {
    @Resource
    private SupplierProductActivityMapper supplierProductActivityMapper;
    @Resource
    private DistributorProductActivityMapper distributorProductActivityMapper;
    @Resource
    private ISysInfService sysInfService;
    @Resource
    private ISupplierProductActivityService supplierProductActivityService;
    @Resource
    private IDistributorProductActivityService distributorProductActivityService;
    @Resource
    private IDistributorProductActivityStockService distributorProductActivityStockService;
    @Resource
    private IDistributorProductActivityPriceService distributorProductActivityPriceService;
    @Resource
    private ISupplierProductActivityStockService supplierProductActivityStockService;
    @Resource
    private ISupplierProductActivityPriceService supplierProductActivityPriceService;
    @Resource
    private IProductSkuService productSkuService;
    @Resource
    private IProductService productService;
    @Resource
    private IDownloadRecordService downloadRecordService;
    @Resource
    private SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    @Resource
    private IProductSkuStockService productSkuStockService;
    @Resource
    private IWarehouseService warehouseService;
    @Resource
    private EsProductMapper esProductMapper;
    @Resource
    private TransactionUtils transactionUtils;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private ProductActiveSupper productActiveSupper;
    @Resource
    private  IProductSkuStockService iProductSkuStockService;
    @Resource
    private  IProductSkuService iProductSkuService;
    @Resource
    private  IWarehouseService iWarehouseService;
    @Resource
    private ProductSupport productSupport;
    @Resource
    private ProductSkuAttachmentMapper productSkuAttachmentMapper;


    public IPage<SupplierProductActivity> selectSupplierListResponseDTO(Page page, ProductActivitySearchDTO bo) {
        return supplierProductActivityMapper.selectListResponseDTO(page,bo);
    }


    public IPage<DistributorProductActivity> selectDistributorListResponseDTO(Page page,ProductActivitySearchDTO bo) {
      return   distributorProductActivityMapper.selectListResponseDTO(page,bo);
    }

    /**
     * 查询活动详情
     * 统一处理供应商活动和分销商活动的详情查询，包括库存信息
     * @param isSupplier 是否是供应商
     * @param activeCode 活动Code集合
     * @return 活动详情列表，包含统一的库存信息
     */
    public List<ProductActivityDetailsDTO> selectProductActivityDetailsDTO(Boolean isSupplier, Set<String> activeCode) {
        if (CollUtil.isEmpty(activeCode)){
            return CollUtil.newArrayList();
        }
        List<ProductActivityDetailsDTO> resultList;
        if (isSupplier){
            // 查询供应商活动详情
            resultList = supplierProductActivityMapper.selectSupplierActivityDetailsDTO(activeCode);
        }else {
            // 查询分销商活动详情
            resultList = supplierProductActivityMapper.selectDistributorActivityDetailsDTO(activeCode);
        }
        return resultList;
    }


    public List<AdminProductActivityWarehouseExportDTO> selectProductActivityWarehouseExport(ProductActivitySearchDTO bo) {
        return supplierProductActivityMapper.selectProductActivityWarehouseExport(bo);
    }


    public List<AdminProductActivityListExportDTO> selectAdminProductActivityListExport(ProductActivitySearchDTO bo) {
        return supplierProductActivityMapper.selectAdminProductActivityListExport(bo);
    }

    public List<AdminProductActivityDetailsExportDTO> selectAdminProductActivityDetailsExport(
        ProductActivitySearchDTO bo) {
        return supplierProductActivityMapper.selectAdminProductActivityDetailsExport(bo);
    }

    public List<DistributorProductActivityDetailsExportDTO> selectDistributorProductActivityDetailsExport(
        ProductActivitySearchDTO bo) {
        return distributorProductActivityMapper.selectDistributorProductActivityDetailsExport(bo);
    }

    public List<DistributorProductActivityListExportDTO> selectDistributorProductActivityListExport(
        ProductActivitySearchDTO bo) {
        return distributorProductActivityMapper.selectDistributorProductActivityListExport(bo);
    }

    /**
     * 供应商活动导入
     * @param file
     */
    public void supplierProductActiveImport(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            String fileName = file.getOriginalFilename();
            // 获取文件的大小
            String fileSize = String.valueOf(file.getSize());
            ExcelUtil.importExcel(inputStream, SupplierProductActivityImportDTO.class, new SupperProductActiveImportListener(
                supplierProductActivityService,
                supplierProductActivityStockService,
                supplierProductActivityPriceService,
                productSkuService,
                downloadRecordService,
                productService,
                siteCountryCurrencyMapper,
                productSkuStockService,
                warehouseService,
                esProductMapper,
                transactionUtils,
                fileName,
                fileSize));
        } catch (IOException e) {
            log.error("供应商活动导入excel导入出现异常，文件[{}]导入失败", file.getOriginalFilename(), e);
            throw new ServiceException("文件导入失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("未知错误", e);
            throw new ServiceException("文件导入异常：" + e.getClass());
        }
    }

    /**
     * 供应商活动审核锁库存
     * @param supplierActivityCode 供应商活动编码
     * @return Boolean
     */
    public Boolean erpProductLockInventoryReserved(String supplierActivityCode){
        List<ErpProductLockInventoryReleaseRequest> successRecords = new ArrayList<>();
        SupplierProductActivity one=null;
        try {
            LambdaQueryWrapper<SupplierProductActivity> q = new LambdaQueryWrapper<>();
            q.eq(SupplierProductActivity::getSupplierActivityCode,supplierActivityCode);
            q.eq(SupplierProductActivity::getDelFlag,0);
            one = supplierProductActivityService.getOne(q);
            if (ObjectUtil.isEmpty(one)){
                throw new RuntimeException(StrUtil.format("[ERP活动库存锁定],活动编号:{} 不存在",supplierActivityCode));
            }
            //查询活动下面的有效仓库
            LambdaQueryWrapper<SupplierProductActivityStock> qq= new LambdaQueryWrapper<>();
            qq.eq(SupplierProductActivityStock::getSupplierActivityCode,supplierActivityCode);
            qq.eq(SupplierProductActivityStock::getDelFlag,0);
            List<SupplierProductActivityStock> stocks = supplierProductActivityStockService.getBaseMapper().selectList(qq);
            if (CollUtil.isEmpty(stocks)) {
                throw new RuntimeException(StrUtil.format("[ERP活动库存锁定],活动:{} 锁货库存信息不存在",supplierActivityCode));
            }

            log.info("[ERP活动库存锁定] 开始处理活动:{}, 需要锁定{}个仓库的库存", supplierActivityCode, stocks.size());
            // 批量锁库存，只有当所有请求都成功时才返回true
            // 如果任何一个失败，则回滚之前成功的锁库存操作
            for (int i = 0; i < stocks.size(); i++) {
                SupplierProductActivityStock stock = stocks.get(i);
                log.info("[ERP活动库存锁定] 正在处理第{}/{}个仓库: {}", i + 1, stocks.size(), stock.getWarehouseCode());

                //构建锁货请求体
                ErpProductLockInventoryReservedRequest lockRequest = new ErpProductLockInventoryReservedRequest();
                lockRequest.setOrgWarehouseCode(stock.getWarehouseCode());
                lockRequest.setInventoryType(0);
                lockRequest.setSrcLabel(90);
                lockRequest.setSourceNo(one.getSupplierTenantId()+"_"+stock.getId());

                ErpProductLockInventoryReservedRequest.ProductItemDto productItemDto = new ErpProductLockInventoryReservedRequest.ProductItemDto();
                productItemDto.setQuantity(stock.getQuantityTotal());
                productItemDto.setSku(one.getProductSku());
                productItemDto.setTrackCode(lockRequest.getSourceNo()+"_"+productItemDto.getSku());
                lockRequest.setProductItemList(CollUtil.newArrayList(productItemDto));

                //开始调用ERP锁库存接口
                Boolean lockResult = productActiveSupper.erpProductLockRequest(SysInfEnum.ERP_PRODUCT_LOCK_INVENTORY_RESERVED, JSONUtil.toJsonStr(lockRequest), true);
                if (lockResult) {
                    // 锁库存成功，记录成功信息用于可能的回滚
                    ErpProductLockInventoryReleaseRequest successRecord = new ErpProductLockInventoryReleaseRequest();
                    successRecord.setSupplierActivityStockId(stock.getId());
                    successRecord.setOrgWarehouseCode(stock.getWarehouseCode());
                    successRecord.setInventoryType(0);
                    successRecord.setSrcLabel(90);
                    successRecord.setSourceNo(one.getSupplierTenantId()+"_"+stock.getId());
                    successRecord.setQuantity(stock.getQuantityTotal());
                    successRecord.setSku(one.getProductSku());
                    successRecord.setTrackCode(successRecord.getSourceNo()+"_"+successRecord.getSku());
                    successRecords.add(successRecord);
                    log.info("[ERP活动库存锁定] 仓库:{} 锁库存成功，数量:{}", stock.getWarehouseCode(), stock.getQuantityTotal());
                    //更新商品库存表的库存锁货数量
                    productSkuStockService.updateProductLockNum( stock.getQuantityTotal(),stock.getWarehouseSystemCode(),one.getProductSkuCode(),stock.getSupportedLogistics(),true);
                } else {
                    // 锁库存失败，需要回滚之前成功的操作
                    log.error("[ERP活动库存锁定] 仓库:{} 锁库存失败，开始回滚之前成功的{}个锁库存操作",
                        stock.getWarehouseCode(), successRecords.size());
                    if (CollUtil.isNotEmpty(successRecords)){
                        // 回滚之前成功的ERP锁定操作
                        rollbackErpLocksWithLocalStock(supplierActivityCode, successRecords, one.getProductSku());
                    }
                    return false;
                }
            }
            log.info("[ERP活动库存锁定] 活动:{} 所有仓库锁库存成功，共处理{}个仓库", supplierActivityCode, successRecords.size());
            return true;
        } catch (Exception e) {
            log.error("[ERP活动库存锁定] 活动:{} 处理异常，开始回滚已成功的{}个锁库存操作",
                supplierActivityCode, successRecords.size(), e);
            // 异常情况下也需要回滚ERP和本地库存
            if (!successRecords.isEmpty()) {
                rollbackErpLocksWithLocalStock(supplierActivityCode, successRecords, one.getProductSku());
            }
            return false;
        }
    }
    /**
     * 添加供应商活动
     * @param dto
     */
    public void addSupplierProductActive(SupplierProductActivityAddDTO dto) {
        //先校验参数
        checkSupplierProductActivityAddDTO(dto);
        Map<String, Integer> logisticsQuantityMap = dto.getStockList().stream()
                                                       .filter(stock -> stock.getQuantityTotal() != null)
                                                       .collect(Collectors.groupingBy(
                                                           SupplierProductActivityAddDTO.SupplierProductActivityStockAddDTO::getSupportedLogistics,
                                                           Collectors.summingInt(stock -> stock.getQuantityTotal())
                                                       ));

        Integer pickUpLocked = logisticsQuantityMap.getOrDefault(SupportedLogisticsEnum.PickUpOnly.name(), 0);
        Integer dropShippingLocked = logisticsQuantityMap.getOrDefault(SupportedLogisticsEnum.DropShippingOnly.name(), 0);

        //构建插入体
        SupplierProductActivity sa = new SupplierProductActivity();
        long id = IdUtil.getSnowflakeNextId();
        String code = LoginHelper.getTenantId() + "-" + ProductActiveCodeUtil.generate();
        sa.setId(id);
        sa.setSupplierTenantId(LoginHelper.getTenantId());
        sa.setActivityName(dto.getActivityName());
        sa.setActivityType(ProductActivityTypeEnum.StockLock.name());
        if (ObjectUtil.equals(dto.getBusinessType(), 2)){
            sa.setActivityState(ProductActivityStateEnum.PendingReview.name());
        }else {
            sa.setActivityState(ProductActivityStateEnum.Draft.name());
        }
        sa.setProductName(dto.getProductName());
        sa.setProductImg(dto.getProductImg());
        sa.setProductSkuCode(dto.getProductSkuCode());
        sa.setProductCode(dto.getProductCode());
        sa.setProductSku(dto.getProductSku());
        sa.setSite(dto.getSite());
        sa.setCurrencySymbol(dto.getCurrencySymbol());
        sa.setActivityDay(dto.getActivityDay());
        sa.setFreeStoragePeriod(dto.getFreeStoragePeriod());
        sa.setQuantityMinimum(dto.getQuantityMinimum());
        sa.setSupplierActivityCode(code);
        sa.setSupplierActivityStorageFee(dto.getSupplierActivityStorageFee());
        //锁库存总数= 每个仓库的锁货数量相加
        sa.setPickupQuantityLocked(pickUpLocked);
        sa.setDropShippingQuantityLocked(dropShippingLocked);
        sa.setPickupLockedUsed(0);
        sa.setDropShippingLockedUsed(0);
        supplierProductActivityService.save(sa);

        //处理价格
        SupplierProductActivityPrice spap = new SupplierProductActivityPrice();
        spap.setId(IdUtil.getSnowflakeNextId());
        spap.setSupplierTenantId(LoginHelper.getTenantId());
        spap.setSupplierActivityCode(code);
        spap.setSupplierActivityId(id);
        spap.setSupplierActivityUnitPrice(dto.getSupplierActivityUnitPrice());
        spap.setSupplierActivityOperationFee(dto.getSupplierActivityOperationFee());
        spap.setSupplierActivityFinalDeliveryFee(dto.getSupplierActivityFinalDeliveryFee());
        spap.setSupplierActivityPickUpPrice(dto.getSupplierActivityUnitPrice().add(dto.getSupplierActivityOperationFee()));
        spap.setSupplierActivityDropShippingPrice(dto.getSupplierActivityUnitPrice().add(dto.getSupplierActivityOperationFee()).add(dto.getSupplierActivityFinalDeliveryFee()));
        supplierProductActivityPriceService.save(spap);
        //处理库存
        dto.getStockList().forEach(s->{
            SupplierProductActivityStock spas = new SupplierProductActivityStock();
            spas.setId(IdUtil.getSnowflakeNextId());
            spas.setSupplierActivityId(id);
            spas.setSupplierActivityCode(code);
            spas.setProductSkuStockId(s.getProductSkuStockId());
            spas.setWarehouseCode(s.getWarehouseCode());
            spas.setWarehouseSystemCode(s.getWarehouseSystemCode());
            spas.setSupportedLogistics(s.getSupportedLogistics());
            spas.setQuantityTotal(s.getQuantityTotal());
            spas.setQuantitySold(0);
            spas.setQuantitySurplus(s.getQuantityTotal());
            supplierProductActivityStockService.save(spas);
        });
    }

    /**
     * 校验供应商活动添加参数
     * @param dto
     */
    private void checkSupplierProductActivityAddDTO(SupplierProductActivityAddDTO dto) {
        if (ObjectUtil.isEmpty(dto)){
            throw new RuntimeException("请求体不能为空");
        }
        if (CollUtil.isEmpty(dto.getStockList())){
            throw new RuntimeException("库存信息不能为空");
        }
        //商品信息不在校验，因为是从商品列表带过来的
        //校验仓库信息
        dto.getStockList().forEach(s->{
            SkuStock skuStock = productSkuStockService.getBaseMapper()
                                                      .getSkuStockBySiteAndWarehouse(dto.getProductSkuCode(), dto.getSite(), s.getWarehouseSystemCode());
            if (ObjectUtil.isEmpty(skuStock)){
                throw new RuntimeException(StrUtil.format("仓库:{} 无库存",s.getWarehouseCode()));
            }
            if (ObjectUtil.equals(s.getSupportedLogistics(), SupportedLogisticsEnum.PickUpOnly.name())){
                if (skuStock.getPickUpStockTotal() < s.getQuantityTotal()){
                    throw new RuntimeException(StrUtil.format("发货方式:{},仓库编码:{},可锁定库存数不能大于仓库库存总数",s.getSupportedLogistics(),s.getWarehouseCode()));
                }
            }
            if (ObjectUtil.equals(s.getSupportedLogistics(), SupportedLogisticsEnum.DropShippingOnly.name())){
                if (skuStock.getDropShippingStockTotal()  < s.getQuantityTotal()){
                    throw new RuntimeException(StrUtil.format("发货方式:{},仓库编码:{},可锁定库存数不能大于仓库库存总数",s.getSupportedLogistics(),s.getWarehouseCode()));
                }
            }
            //校验通过进行赋值，方便后面插入
            s.setWarehouseSystemCode(skuStock.getWarehouseSystemCode());
            s.setProductSkuStockId(skuStock.getId());
        });

    }

    /**
     * 供应商活动编辑
     * @param dto
     */
    public void updateSupplierProductActive(@Valid SupplierProductActivityUpdateDTO dto) {
        //判读活动的状态
        SupplierProductActivity active = supplierProductActivityService.getByActivityCode(dto.getSupplierActivityCode());
        if (ObjectUtil.isEmpty(active)){
            throw new RuntimeException("活动不存在");
        }
        List<String> name = List.of(ProductActivityStateEnum.Canceled.name(),
            ProductActivityStateEnum.SoldOut.name(),
            ProductActivityStateEnum.UnderReview.name(),
            ProductActivityStateEnum.PendingReview.name());

        if (name.contains(active.getActivityState())){
            throw new RuntimeException("已取消/已售罄/待审核/审核中 的活动不能修改");
        }
        active.setActivityName(dto.getActivityName());
        active.setQuantityMinimum(dto.getQuantityMinimum());
        active.setSupplierActivityStorageFee(dto.getSupplierActivityStorageFee());
        if (dto.getBusinessType() == 2) {
            active.setActivityState(ProductActivityStateEnum.PendingReview.name());
        } else {
            active.setActivityState(ProductActivityStateEnum.Draft.name());
        }
        supplierProductActivityService.updateById(active);
        SupplierProductActivityPrice one = supplierProductActivityPriceService.getOne(new LambdaQueryWrapper<SupplierProductActivityPrice>()
            .eq(SupplierProductActivityPrice::getSupplierActivityCode, dto.getSupplierActivityCode()));
        if (ObjectUtil.isNotNull(one)){
            one.setSupplierActivityUnitPrice(dto.getSupplierActivityUnitPrice());
            one.setSupplierActivityOperationFee(dto.getSupplierActivityOperationFee());
            one.setSupplierActivityFinalDeliveryFee(dto.getSupplierActivityFinalDeliveryFee());
            one.setSupplierActivityPickUpPrice(dto.getSupplierActivityUnitPrice().add(dto.getSupplierActivityOperationFee()));
            one.setSupplierActivityDropShippingPrice(dto.getSupplierActivityUnitPrice().add(dto.getSupplierActivityOperationFee()).add(dto.getSupplierActivityFinalDeliveryFee()));
            supplierProductActivityPriceService.updateById(one);
        }
        //查询当前供应商活动关联的分销商活动
//        List<DistributorProductActivity> supplierActivity = distributorProductActivityService.getBySupplierActivityCode(dto.getSupplierActivityCode());
//        if (CollUtil.isNotEmpty(supplierActivity)){
//            supplierActivity.forEach(s->{
//                s.setDistributorActivityStorageFee(s.getDistributorActivityStorageFee());
//                distributorProductActivityService.updateById(s);
//                //更新每个分销商的价格
//                DistributorProductActivityPrice price = distributorProductActivityPriceService.getOne(new LambdaQueryWrapper<DistributorProductActivityPrice>()
//                    .eq(DistributorProductActivityPrice::getDistributorActivityCode, s.getDistributorActivityCode()));
//                price.setDistributorActivityUnitPrice(dto.getSupplierActivityUnitPrice());
//                price.setDistributorActivityOperationFee(dto.getSupplierActivityOperationFee());
//                price.setDistributorActivityFinalDeliveryFee(dto.getSupplierActivityFinalDeliveryFee());
//                price.setDistributorActivityPickUpPrice(dto.getSupplierActivityUnitPrice().add(dto.getSupplierActivityOperationFee()));
//                price.setDistributorActivityFinalDeliveryFee(dto.getSupplierActivityUnitPrice().add(dto.getSupplierActivityOperationFee()).add(dto.getSupplierActivityFinalDeliveryFee()));
//                distributorProductActivityPriceService.updateById(price);
//            });
//        }

    }

    /**
     * 更新活动异常状态(联动更新分销商活动)
     * @param i 异常状态
     * @param productSkuCode 商品编码
     */
    public void updateProductActivityException(int i,String productSkuCode,String warehouseSystemCode) {
        supplierProductActivityService.getBaseMapper().updateSupplierProductActivityException(i,productSkuCode,warehouseSystemCode);
        //更新分销商的
        distributorProductActivityService.getBaseMapper().updateDistributorProductActivityException(i,productSkuCode,warehouseSystemCode);
    }

    /**
     * 更新活动异常状态(联动更新分销商活动)
     * @param productSkuCode 商品编码
     */
    public void updateProductActivityExceptionByStock(String productSkuCode) {
        supplierProductActivityService.getBaseMapper().updateProductActivityExceptionByStock(productSkuCode);
    }

    /**
     * 更新活动异常状态(联动更新分销商活动)
     * @param i 异常状态
     * @param id 商品库存表ID
     */
    public void updateProductActivityStockException(int i,long id) {
        supplierProductActivityService.getBaseMapper().updateProductActivityStockException(i,id);
    }

    /**
     * 供应商活动取消
     * @param supplierProductActiveCode 供应商活动编码
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelSupplierProductActive(String supplierProductActiveCode) {
        log.info("开始取消供应商活动，活动编码：{}", supplierProductActiveCode);

        // 1. 获取供应商活动信息
        SupplierProductActivity activity = supplierProductActivityService.getByActivityCode(supplierProductActiveCode);
        if (ObjectUtil.isEmpty(activity)){
            log.error("供应商活动不存在，活动编码：{}", supplierProductActiveCode);
            throw new RuntimeException("活动不存在");
        }

        // 2. 获取关联的分销商活动列表
        List<DistributorProductActivity> distributorActivity = distributorProductActivityService.getBySupplierActivityCode(supplierProductActiveCode);

        // 3. 获取活动库存信息
        List<SupplierProductActivityStock> stocks = getActivityStocks(supplierProductActiveCode);

        log.info("供应商活动状态：{}，关联分销商活动数量：{}，库存记录数量：{}",
            activity.getActivityState(), distributorActivity.size(), stocks.size());

        // 4. 根据活动状态执行不同的取消逻辑
        switch (activity.getActivityState()){
            case "Draft":
            case "PendingReview":
            case "UnderReview":
            case "NotApproved":
                // 这几个状态直接取消，无需释放库存
                log.info("活动状态为{}，直接取消", activity.getActivityState());
                updateActivityStatusToCanceled(activity, supplierProductActiveCode);
                break;
            case "Published":
                // 已发布状态需要释放库存后取消
                log.info("活动状态为Published，需要释放库存后取消");
                releaseInventoryAndUpdateStatus(activity, stocks, supplierProductActiveCode);
                break;
            case "InProgress":
                // 进行中状态需要验证分销商活动状态，然后释放库存
                log.info("活动状态为InProgress，需要验证分销商活动状态后释放库存");
                validateDistributorActivitiesForInProgressCancellation(distributorActivity);
                releaseInventoryAndUpdateStatus(activity, stocks, supplierProductActiveCode);
                break;

            default:
                log.error("不支持的活动状态：{}", activity.getActivityState());
                throw new RuntimeException("当前活动状态不支持取消" + activity.getActivityState());
        }

        log.info("供应商活动取消完成，活动编码：{}", supplierProductActiveCode);
    }

    /**
     * 获取活动库存信息
     * @param supplierProductActiveCode 供应商活动编码
     * @return 库存列表
     */
    private List<SupplierProductActivityStock> getActivityStocks(String supplierProductActiveCode) {
        LambdaQueryWrapper<SupplierProductActivityStock> q = new LambdaQueryWrapper<>();
        q.eq(SupplierProductActivityStock::getSupplierActivityCode, supplierProductActiveCode);
        return supplierProductActivityStockService.getBaseMapper().selectList(q);
    }

    /**
     * 验证分销商活动是否都是进行中状态（用于InProgress状态的取消验证）
     * @param distributorActivity 分销商活动列表
     */
    private void validateDistributorActivitiesForInProgressCancellation(List<DistributorProductActivity> distributorActivity) {
        boolean hasNonInProgressActivity = distributorActivity.stream()
                                                              .anyMatch(da -> ProductActivityStateEnum.InProgress.name().equals(da.getActivityState()));

        if (hasNonInProgressActivity) {
            log.error("存在进行中状态的分销商活动，不能取消");
            throw new RuntimeException("有分销商活动是进行中状态，不能取消");
        }
        log.info("所有分销商活动都是进行中状态，可以继续取消流程");
    }

    /**
     * 释放库存并更新活动状态
     * @param activity 供应商活动
     * @param stocks 库存列表
     * @param supplierProductActiveCode 供应商活动编码
     */
    @Transactional
    public void releaseInventoryAndUpdateStatus(SupplierProductActivity activity,
                                                List<SupplierProductActivityStock> stocks,
                                                String supplierProductActiveCode) {
        activity.setActivityState(ProductActivityStateEnum.Canceling.name());
        supplierProductActivityService.updateById(activity);
        ThreadUtil.execAsync(()->{
            boolean isAllSuccess = true;
            if (CollUtil.isNotEmpty(stocks)) {
                log.info("开始释放库存，库存记录数量：{}", stocks.size());
                for (SupplierProductActivityStock stock : stocks) {
                    try {
                        //供应商活动释放全量库存
                        Boolean releaseResult = productActiveSupper.erpProductLockInventoryRelease(supplierProductActiveCode, stock.getId(), stock.getQuantityTotal());
                        if (!releaseResult) {
                            isAllSuccess = false;
                            log.error("库存释放失败，活动编码：{}，仓库编码：{}，数量：{}",
                                supplierProductActiveCode, stock.getWarehouseCode(), stock.getQuantityTotal());
                            updateProductActivityStockException(ProductActivityExceptionEnum.ERP_RELEASE_EXCEPTION.getCode(), stock.getProductSkuStockId());
                            break;
                        } else {
                            log.info("库存释放成功，活动编码：{}，仓库编码：{}，数量：{}",
                                supplierProductActiveCode, stock.getWarehouseCode(), stock.getQuantityTotal());
                            //将商品库存表的锁货库存也释放
                            ProductSkuStock skuStock = TenantHelper.ignore(() -> productSkuStockService.getBaseMapper()
                                                                                                       .selectById(stock.getProductSkuStockId()));
                            if (stock.getSupportedLogistics().equals(SupportedLogisticsEnum.PickUpOnly.name())){
                                skuStock.setPickupLockUsed(skuStock.getPickupLockUsed()-stock.getQuantityTotal());
                                productSkuStockService.updateById(skuStock);
                            }
                            if (stock.getSupportedLogistics().equals(SupportedLogisticsEnum.DropShippingOnly.name())){
                                skuStock.setDropShippingLockUsed(skuStock.getDropShippingLockUsed()-stock.getQuantityTotal());
                                productSkuStockService.updateById(skuStock);
                            }
                            updateProductActivityStockException(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode(), stock.getProductSkuStockId());
                        }
                    } catch (Exception e) {
                        isAllSuccess = false;
                        log.error("库存释放异常，活动编码：{}，仓库编码：{}",
                            supplierProductActiveCode, stock.getWarehouseCode(), e);
                        updateProductActivityException(ProductActivityExceptionEnum.ERP_RELEASE_EXCEPTION.getCode(), activity.getProductSkuCode(), null);
                        break;
                    }
                }
                updateProductActivityExceptionByActivity(activity.getSupplierActivityCode());
            } else {
                log.info("无库存记录，跳过库存释放");
            }

            // 如果所有库存都释放成功，则更新活动状态
            if (isAllSuccess) {
                log.info("所有库存释放成功，更新活动状态为已取消");
                updateActivityStatusToCanceled(activity, supplierProductActiveCode);
            } else {
                log.error("库存释放失败，活动状态保持不变，已设置异常状态");
            }

        });

    }

    /**
     * 根据活动关联仓库异常更新活动异常
     * @param supplierActivityCode 供应商活动编码
     */
    private void updateProductActivityExceptionByActivity(String supplierActivityCode) {
        supplierProductActivityService.getBaseMapper().updateProductActivityExceptionByActivity(supplierActivityCode);
    }


    /**
     * 更新活动状态为已取消
     * @param activity 供应商活动
     * @param supplierProductActiveCode 供应商活动编码
     */
    private void updateActivityStatusToCanceled(SupplierProductActivity activity, String supplierProductActiveCode) {
        try {
            // 更新供应商活动状态
            activity.setActivityState(ProductActivityStateEnum.Canceled.name());
            supplierProductActivityService.updateById(activity);

            // 更新关联的分销商活动状态
            distributorProductActivityService.updateDistributorProductActivityState(
                supplierProductActiveCode, ProductActivityStateEnum.Canceled.name());

            log.info("活动状态更新成功，供应商活动编码：{}", supplierProductActiveCode);
        } catch (Exception e) {
            log.error("活动状态更新失败，供应商活动编码：{}", supplierProductActiveCode, e);
            throw new RuntimeException("活动状态更新失败", e);
        }
    }

    /**
     * 添加分销商活动
     * @param dto
     */
    @Transactional
    public void addDistributorProductActive(@Valid DistributorProductActivityAddDTO dto) {
        // 1. 验证供应商活动状态
        SupplierProductActivity activity = supplierProductActivityService.getByActivityCode(dto.getSupplierActivityCode());
        if (ObjectUtil.isEmpty(activity)) {
            throw new RuntimeException("供应商活动不存在");
        }
        List<String> validStates = List.of("Published", "InProgress");
        if (!validStates.contains(activity.getActivityState())){
            throw new RuntimeException("活动状态异常，不能添加,仅支持已发布和进行中的供应商活动");
        }

        List<String> logistics = List.of("PickUpOnly", "DropShippingOnly");
        if (!logistics.contains(dto.getSupportedLogistics())){
            throw new RuntimeException("非法的发货方式"+dto.getSupportedLogistics());
        }
        // 2. 验证发货方式支持
        List<SupplierProductActivityStock> supplierStocks = supplierProductActivityStockService.getBySupplierActivityCode(
            dto.getSupplierActivityCode(), dto.getSupportedLogistics());
        if (CollUtil.isEmpty(supplierStocks)){
            throw new RuntimeException("不支持的发货方式: " + dto.getSupportedLogistics());
        }
        List<DistributorProductActivityAddDTO.DistributorProductActivityStockAddDTO> stockList = dto.getStockList();
        boolean match = stockList.stream()
                                 .map(DistributorProductActivityAddDTO.DistributorProductActivityStockAddDTO::getWarehouseCode)
                                 .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                                 .entrySet().stream()
                                 .anyMatch(entry -> entry.getValue() > 1);

        if (match) {
            throw new RuntimeException("仓库编码不能重复");
        }
        Map<Long, SupplierProductActivityStock> stockMap = supplierStocks.stream()
                                                                         .filter(Objects::nonNull)
                                                                         .filter(stock -> ObjectUtil.isNotEmpty(stock.getWarehouseSystemCode()))
                                                                         .collect(Collectors.toMap(
                                                                             SupplierProductActivityStock::getId,
                                                                             stock -> stock,
                                                                             (existing, replacement) -> existing));
        // 先进行基础验证，不涉及库存检查
        stockList.forEach(s->{
            SupplierProductActivityStock activityStock = stockMap.get(s.getSupplierActivityStockId());
            if (ObjectUtil.isEmpty(activityStock)){
                throw new RuntimeException(StrUtil.format("仓库不存在:{}",s.getWarehouseCode()));
            }
            if (!StrUtil.equals(s.getWarehouseCode(),activityStock.getWarehouseCode())){
                throw new RuntimeException(StrUtil.format("仓库编码与供应商活动仓库不一致:{} ",s.getWarehouseCode()));
            }
            if (s.getQuantityTotal()<activity.getQuantityMinimum()){
                throw new RuntimeException(StrUtil.format("仓库:{} 锁货数量不能小于活动最小起订量:{}",s.getWarehouseCode(),activity.getQuantityMinimum()));
            }
            s.setWarehouseSystemCode(activityStock.getWarehouseSystemCode());
        });

        int num=0;
        // 使用数据库乐观锁机制，原子性地检查库存并更新已分配数量
        // 记录成功更新的库存ID，用于失败时回滚
        List<StockUpdateRecord> successUpdatedRecords = new ArrayList<>();
        try {
            for (DistributorProductActivityAddDTO.DistributorProductActivityStockAddDTO addDTO : stockList) {
                num=num+addDTO.getQuantityTotal();
                // 使用乐观锁更新，如果剩余库存不足则更新失败
                int updateCount = supplierProductActivityStockService.getBaseMapper()
                                                                     .updateQuantitySoldWithOptimisticLock(addDTO.getQuantityTotal(), addDTO.getSupplierActivityStockId());
                if (updateCount == 0) {
                    // 更新失败说明库存不足或数据已被其他线程修改
                    SupplierProductActivityStock currentStock = supplierProductActivityStockService.getById(addDTO.getSupplierActivityStockId());
                    if (currentStock == null) {
                        throw new RuntimeException(StrUtil.format("仓库库存记录不存在:{}",addDTO.getWarehouseCode()));
                    }
                    if (currentStock.getQuantitySurplus() < addDTO.getQuantityTotal()) {
                        throw new RuntimeException(StrUtil.format("仓库:{} 可用锁货库存不足，当前剩余:{}，需要:{}",
                            addDTO.getWarehouseCode(), currentStock.getQuantitySurplus(), addDTO.getQuantityTotal()));
                    } else {
                        // 并发冲突，重试
                        throw new RuntimeException(StrUtil.format("仓库:{} 库存分配冲突，请重试",addDTO.getWarehouseCode()));
                    }
                }
                // 记录成功更新的库存信息，用于可能的回滚
                successUpdatedRecords.add(new StockUpdateRecord(addDTO.getSupplierActivityStockId(),
                    addDTO.getQuantityTotal(), addDTO.getWarehouseCode()));
                log.info("库存分配成功: 仓库:{}, 数量:{}", addDTO.getWarehouseCode(), addDTO.getQuantityTotal());
            }
        } catch (Exception e) {
            // 发生异常时回滚已成功的库存更新
            if (!successUpdatedRecords.isEmpty()) {
                log.warn("库存分配失败，开始回滚已成功的{}个库存更新", successUpdatedRecords.size());
                rollbackStockUpdates(successUpdatedRecords);
            }
            throw e;
        }
        long id = IdUtil.getSnowflakeNextId();
        String code =activity.getSupplierActivityCode() + "-" + ProductActiveCodeUtil.generate();

        DistributorProductActivity pa = BeanUtil.copyProperties(activity, DistributorProductActivity.class);
        pa.setId(id);
        pa.setDistributorTenantId(LoginHelper.getTenantId());
        pa.setDistributorActivityCode(code);
        pa.setSupplierActivityId(activity.getId());
        if (dto.getSupportedLogistics().equals(SupportedLogisticsEnum.PickUpOnly.name())){
            pa.setDropShippingQuantityLocked(0);
            pa.setDropShippingLockedUsed(0);
            pa.setPickupQuantityLocked(num);
            pa.setPickupLockedUsed(0);
            activity.setPickupLockedUsed(activity.getPickupLockedUsed()+num);
            pa.setSupportedLogistics(SupportedLogisticsEnum.PickUpOnly.name());
        }else {
            pa.setPickupQuantityLocked(0);
            pa.setPickupLockedUsed(0);
            pa.setDropShippingQuantityLocked(num);
            pa.setDropShippingLockedUsed(0);
            activity.setDropShippingLockedUsed(activity.getDropShippingLockedUsed()+num);
            pa.setSupportedLogistics(SupportedLogisticsEnum.DropShippingOnly.name());
        }
        Date startTime = new Date();
        pa.setActiveStartTime(startTime);
        pa.setActiveEndTime(DateUtil.offsetDay(startTime, activity.getActivityDay()));
        pa.setActivityState(ProductActivityStateEnum.InProgress.name());
        pa.setDistributorActivityStorageFee(activity.getSupplierActivityStorageFee());
        //构建价格
        SupplierProductActivityPrice supplierPrice = supplierProductActivityPriceService.getOne(new LambdaQueryWrapper<SupplierProductActivityPrice>()
            .eq(SupplierProductActivityPrice::getSupplierActivityCode, dto.getSupplierActivityCode()));
        DistributorProductActivityPrice price = BeanUtil.copyProperties(supplierPrice, DistributorProductActivityPrice.class);
        price.setId(IdUtil.getSnowflakeNextId());
        price.setDistributorActivityCode(code);
        price.setDistributorActivityId(id);
        price.setDistributorTenantId(LoginHelper.getTenantId());
        price.setDistributorActivityUnitPrice(supplierPrice.getSupplierActivityUnitPrice());
        price.setDistributorActivityOperationFee(supplierPrice.getSupplierActivityOperationFee());
        price.setDistributorActivityFinalDeliveryFee(supplierPrice.getSupplierActivityFinalDeliveryFee());
        price.setDistributorActivityPickUpPrice(supplierPrice.getSupplierActivityPickUpPrice());
        price.setDistributorActivityDropShippingPrice(supplierPrice.getSupplierActivityDropShippingPrice());

        distributorProductActivityPriceService.save(price);
        //构建库存
        stockList.forEach(s->{
            DistributorProductActivityStock ds=new DistributorProductActivityStock();
            ds.setId(IdUtil.getSnowflakeNextId());
            ds.setDistributorActivityId(id);
            ds.setDistributorActivityCode(code);
            ds.setSupplierActivityStockId(s.getSupplierActivityStockId());
            ds.setWarehouseCode(s.getWarehouseCode());
            ds.setWarehouseSystemCode(s.getWarehouseSystemCode());
            ds.setSupportedLogistics(dto.getSupportedLogistics());
            ds.setQuantityTotal(s.getQuantityTotal());
            ds.setQuantitySold(0);
            ds.setQuantitySurplus(s.getQuantityTotal());
            distributorProductActivityStockService.save(ds);
        });
        //处理押金
        if (dto.getSupportedLogistics().equals(SupportedLogisticsEnum.PickUpOnly.name())){
            //自提押金
            BigDecimal decimal = (supplierPrice.getSupplierActivityUnitPrice()
                                               .add(supplierPrice.getSupplierActivityOperationFee()))
                .multiply(BigDecimal.valueOf(num)).multiply(BigDecimal.valueOf(0.2));
            log.info("自提押金:{}",decimal);
            pa.setDepositPaidTotal(decimal);
            activity.setDepositPaidTotal(activity.getDepositPaidTotal().add(decimal));
        }else {
            //代发押金
            BigDecimal decimal = (supplierPrice.getSupplierActivityUnitPrice()
                                               .add(supplierPrice.getSupplierActivityOperationFee()).add(supplierPrice.getSupplierActivityFinalDeliveryFee()))
                .multiply(BigDecimal.valueOf(num)).multiply(BigDecimal.valueOf(0.2));
            log.info("代发押金:{}",decimal);
            pa.setDepositPaidTotal(decimal);
            activity.setDepositPaidTotal(activity.getDepositPaidTotal().add(decimal));
        }
        // 使用原子操作更新供应商活动状态和库存使用情况
        updateSupplierActivityWithConcurrencyControl(activity, num, dto.getSupportedLogistics());
        distributorProductActivityService.save(pa);
        log.info(JSONUtil.toJsonStr(pa));
        ThreadUtil.execAsync(()->{
            // 计算延时时间（毫秒）
            long delayTime = DateUtil.between( new Date(),pa.getActiveEndTime(), DateUnit.MS);
            if (delayTime <= 0) {
                log.warn("活动已过期，不发送延时消息: {} - 结束时间: {}", pa.getDistributorActivityCode(), pa.getActiveEndTime());
                return;
            }
            // 创建消息内容（使用活动编码作为消息体）
            String messageBody = pa.getDistributorActivityCode();
            // 发送消息到TTL队列，设置消息TTL
            rabbitTemplate.convertAndSend(
                RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_EXCHANGE,
                RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_ROUTING_KEY,
                messageBody,
                message -> {
                    // 设置消息TTL
                    message.getMessageProperties().setExpiration(String.valueOf(delayTime));
                    return message;
                }
            );
            log.info("发送活动过期延时消息成功: 活动编码={}, 结束时间={}, 延时={}ms", messageBody, DateUtil.formatDateTime(pa.getActiveEndTime()), delayTime);
        });
    }

    /**
     * 支付前校验活动
     * @param dto
     */
    public void checkDistributorProductActive(@Valid DistributorProductActivityAddDTO dto) {
        // 1. 验证供应商活动状态
        SupplierProductActivity activity = supplierProductActivityService.getByActivityCode(dto.getSupplierActivityCode());
        if (ObjectUtil.isEmpty(activity)) {
            throw new RuntimeException("供应商活动不存在");
        }
        List<String> validStates = List.of("Published", "InProgress");
        if (!validStates.contains(activity.getActivityState())){
            throw new RuntimeException("活动状态异常，不能添加,仅支持已发布和进行中的供应商活动");
        }

        List<String> logistics = List.of(SupportedLogisticsEnum.PickUpOnly.name(), SupportedLogisticsEnum.DropShippingOnly.name());
        if (!logistics.contains(dto.getSupportedLogistics())){
            throw new RuntimeException("非法的发货方式"+dto.getSupportedLogistics());
        }
        // 2. 验证发货方式支持
        List<SupplierProductActivityStock> supplierStocks = supplierProductActivityStockService.getBySupplierActivityCode(
            dto.getSupplierActivityCode(), dto.getSupportedLogistics());
        if (CollUtil.isEmpty(supplierStocks)){
            throw new RuntimeException("不支持的发货方式: " + dto.getSupportedLogistics());
        }
        List<DistributorProductActivityAddDTO.DistributorProductActivityStockAddDTO> stockList = dto.getStockList();
        boolean match = stockList.stream()
                                 .map(DistributorProductActivityAddDTO.DistributorProductActivityStockAddDTO::getWarehouseCode)
                                 .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                                 .entrySet().stream()
                                 .anyMatch(entry -> entry.getValue() > 1);

        if (match) {
            throw new RuntimeException("仓库编码不能重复");
        }
        Map<Long, SupplierProductActivityStock> stockMap = supplierStocks.stream()
                                                                         .filter(Objects::nonNull)
                                                                         .filter(stock -> ObjectUtil.isNotEmpty(stock.getWarehouseSystemCode()))
                                                                         .collect(Collectors.toMap(
                                                                             SupplierProductActivityStock::getId,
                                                                             stock -> stock,
                                                                             (existing, replacement) -> existing));
        stockList.forEach(s->{
            SupplierProductActivityStock activityStock = stockMap.get(s.getSupplierActivityStockId());
            if (ObjectUtil.isEmpty(activityStock)){
                throw new RuntimeException(StrUtil.format("仓库不存在:{}",s.getWarehouseCode()));
            }
            if (!StrUtil.equals(s.getWarehouseCode(),activityStock.getWarehouseCode())){
                throw new RuntimeException(StrUtil.format("仓库编码与供应商活动仓库不一致:{} ",s.getWarehouseCode()));
            }
            if (activityStock.getQuantitySurplus() < s.getQuantityTotal()){
                throw new RuntimeException(StrUtil.format("仓库:{} 可用锁货库存不足",s.getWarehouseCode()));
            }
            if (s.getQuantityTotal()<activity.getQuantityMinimum()){
                throw new RuntimeException(StrUtil.format("仓库:{} 锁货数量不能小于最小起订量",s.getWarehouseCode()));
            }
        });
    }

    /**
     * 分销商活动取消
     * @param distributorActivityCode 分销商活动编码
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelDistributorProductActive(String distributorActivityCode) {
        log.info("开始取消分销商活动: {}", distributorActivityCode);

        // 1. 验证分销商活动状态
        DistributorProductActivity activity = distributorProductActivityService.getByActivityCode(distributorActivityCode);
        if (ObjectUtil.isNull(activity)) {
            throw new RuntimeException("活动不存在");
        }
        if (!ProductActivityStateEnum.InProgress.name().equals(activity.getActivityState())){
            throw new RuntimeException("非进行中的活动不能取消");
        }

        // 2. 验证供应商活动存在
        SupplierProductActivity supplierProductActivity = supplierProductActivityService.getByActivityCode(activity.getSupplierActivityCode());
        if (ObjectUtil.isNull(supplierProductActivity)){
            throw new RuntimeException("供应商活动不存在");
        }

        // 3. 更新分销商活动状态
        activity.setActivityState(ProductActivityStateEnum.Canceled.name());
        distributorProductActivityService.updateById(activity);

        // 4. 使用事务保护的库存回退方法
        rollbackDistributorStockToSupplier(activity.getId(), activity.getSupplierActivityCode());

        log.info("分销商活动取消完成: {}", distributorActivityCode);
    }

    /**
     * 分销商活动过期处理
     * @param distributorActivityCode 分销商活动编码
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleActivityExpire(String distributorActivityCode) {
        log.info("开始处理分销商活动过期: {}", distributorActivityCode);

        // 1. 获取分销商活动信息
        DistributorProductActivity activity = distributorProductActivityService.getByActivityCode(distributorActivityCode);
        if (ObjectUtil.isNull(activity)) {
            log.warn("分销商活动不存在，跳过过期处理: {}", distributorActivityCode);
            return;
        }

        // 2. 只处理进行中的活动
        if (!ProductActivityStateEnum.InProgress.name().equals(activity.getActivityState())) {
            log.info("分销商活动状态非进行中，跳过过期处理: 活动={}, 状态={}",
                distributorActivityCode, activity.getActivityState());
            return;
        }

        // 3. 验证供应商活动存在
        SupplierProductActivity supplierProductActivity = supplierProductActivityService.getByActivityCode(activity.getSupplierActivityCode());
        if (ObjectUtil.isNull(supplierProductActivity)) {
            throw new RuntimeException("供应商活动不存在");
        }

        // 4. 更新分销商活动状态为过期
        activity.setActivityState(ProductActivityStateEnum.Expired.name());
        distributorProductActivityService.updateById(activity);

        // 5. 使用事务保护的库存回退方法
        rollbackDistributorStockToSupplier(activity.getId(), activity.getSupplierActivityCode());

        log.info("分销商活动过期处理完成: {}", distributorActivityCode);
    }

    /**
     * 发送分销商活动过期消息
     * @param distributorProductActiveCode 分销商活动编码
     * @param time 过期时间
     */
    public void sendDistributorActivityExpire(String distributorProductActiveCode,String time) {
        DistributorProductActivity pa = distributorProductActivityService.getByActivityCode(distributorProductActiveCode);
        ThreadUtil.execAsync(()->{
            // 计算延时时间（毫秒）
            long delayTime = DateUtil.between( new Date(),DateUtil.parse(time, "yyyy-MM-dd HH:mm:ss"), DateUnit.MS);
            if (delayTime <= 0) {
                log.error("活动过期时间不能小于当前时间: {} - 结束时间: {}", pa.getDistributorActivityCode(), pa.getActiveEndTime());
                throw new RuntimeException("活动过期时间不能小于当前时间，不发送延时消息");

            }
            // 创建消息内容（使用活动编码作为消息体）
            String messageBody = pa.getDistributorActivityCode();
            // 发送消息到TTL队列，设置消息TTL
            rabbitTemplate.convertAndSend(
                RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_EXCHANGE,
                RabbitMqConstant.DISTRIBUTOR_ACTIVITY_EXPIRE_ROUTING_KEY,
                messageBody,
                message -> {
                    // 设置消息TTL
                    message.getMessageProperties().setExpiration(String.valueOf(delayTime));
                    return message;
                }
            );
            log.info("发送活动过期延时消息成功: 活动编码={}, 结束时间={}, 延时={}ms", messageBody, DateUtil.formatDateTime(pa.getActiveEndTime()), delayTime);
        });

    }

    /**
     * 根据商品编码和站点获取可用的分销商活动
     * @param productSkuCode 商品编码
     * @param site 站点
     * @return
     */
    public List<DistributorProductActivityDetails> getDistributorAvailableActivesBySku(String productSkuCode,String site) {
        if (StrUtil.isEmpty(site) || StrUtil.isEmpty(productSkuCode)){
            return new ArrayList<>();
        }
        return distributorProductActivityService.getBaseMapper().getDistributorAvailableActivesBySku(productSkuCode,site);
    }

    /**
     * 根据商品编码获取可用的分销商活动
     * @param productSkuCode 商品编码
     * @param site 站点
     * @return
     */
    public Map<String,List<DistributorProductActivityDetails>> getDistributorAvailableActivesMapBySku(String productSkuCode,String site) {
        if (StrUtil.isEmpty(site) || StrUtil.isEmpty(productSkuCode)){
            return new HashMap<>();
        }
        List<DistributorProductActivityDetails> activesBySku = TenantHelper.ignore(()->distributorProductActivityService.getBaseMapper().getDistributorAvailableActivesBySku( productSkuCode, site));
        return activesBySku.stream()
                           .filter(Objects::nonNull)
                           .filter(activity -> activity.getSupportedLogistics() != null)
                           .collect(Collectors.groupingBy(
                               DistributorProductActivityDetails::getSupportedLogistics,
                               LinkedHashMap::new,
                               Collectors.collectingAndThen(
                                   Collectors.toList(),
                                   list -> list.stream()
                                               .sorted((a, b) -> {
                                                   if (a.getId() == null && b.getId() == null) return 0;
                                                   if (a.getId() == null) return 1;
                                                   if (b.getId() == null) return -1;
                                                   return b.getId().compareTo(a.getId()); // 降序排列
                                               })
                                               .collect(Collectors.toList())
                               )
                           ));

    }

    /**
     * 回滚ERP锁定操作并同步回滚本地库存
     * 按照您的设计：ERP释放成功后才回滚本地库存，保证数据一致性
     * @param supplierActivityCode 供应商活动编码
     * @param successRecords ERP锁定成功的记录列表
     * @param productSku 商品SKU
     */
    private void rollbackErpLocksWithLocalStock(String supplierActivityCode,
                                                List<ErpProductLockInventoryReleaseRequest> successRecords,
                                                String productSku) {
        log.info("[ERP活动库存锁定] 开始回滚{}个成功的锁库存操作", successRecords.size());

        for (ErpProductLockInventoryReleaseRequest successRecord : successRecords) {
            try {
                // 1. 先调用ERP释放库存
                Boolean erpReleaseSuccess = productActiveSupper.erpProductLockRequest(SysInfEnum.ERP_PRODUCT_LOCK_INVENTORY_RELEASE, JSONUtil.toJsonStr(List.of(successRecord)), false);
                if (erpReleaseSuccess) {
                    // 2. ERP释放成功后，回滚本地库存锁定数量
                    // 需要根据成功记录找到对应的仓库和物流方式信息
                    DistributorProductActivityStock stock = distributorProductActivityStockService.getById(successRecord.getDistributorActivityStockId());
                    if (stock != null) {
                        productSkuStockService.updateProductLockNum(
                            successRecord.getQuantity(),
                            stock.getWarehouseSystemCode(),
                            productSku,
                            stock.getSupportedLogistics(),
                            false
                        );
                        log.info("[ERP活动库存锁定] 仓库:{} 回滚成功，ERP和本地库存已同步", successRecord.getOrgWarehouseCode());
                    } else {
                        log.error("[ERP活动库存锁定] 找不到仓库{}的原始库存记录，本地库存回滚失败", successRecord.getOrgWarehouseCode());
                    }
                } else {
                    // 3. ERP释放失败，本地库存不回滚，保持一致性
                    log.error("[ERP活动库存锁定] 仓库:{} ERP释放失败，本地库存保持锁定状态，请手动处理！活动:{}, 成功记录:{}",
                        successRecord.getOrgWarehouseCode(), supplierActivityCode, JSONUtil.toJsonStr(successRecord));
                }
            } catch (Exception e) {
                log.error("[ERP活动库存锁定] 仓库:{} 回滚异常，请手动处理！", successRecord.getOrgWarehouseCode(), e);
            }
        }
    }
    /**
     * 回滚库存更新操作
     * @param successUpdatedRecords 成功更新的库存记录列表
     */
    private void rollbackStockUpdates(List<StockUpdateRecord> successUpdatedRecords) {
        for (StockUpdateRecord record : successUpdatedRecords) {
            try {
                // 使用负数量来回滚之前的更新
                int rollbackCount = supplierProductActivityStockService.getBaseMapper()
                                                                       .updateQuantitySoldWithOptimisticLock(-record.getQuantity(), record.getStockId());
                if (rollbackCount > 0) {
                    log.info("库存回滚成功: 仓库:{}, 回滚数量:{}", record.getWarehouseCode(), record.getQuantity());
                } else {
                    log.error("库存回滚失败: 仓库:{}, 回滚数量:{}", record.getWarehouseCode(), record.getQuantity());
                }
            } catch (Exception e) {
                log.error("库存回滚异常: 仓库:{}, 回滚数量:{}", record.getWarehouseCode(), record.getQuantity(), e);
            }
        }
    }

    /**
     * 使用并发控制更新供应商活动状态和库存使用情况
     * @param activity 供应商活动
     * @param allocatedQuantity 本次分配的数量
     * @param supportedLogistics 物流方式
     */
    private void updateSupplierActivityWithConcurrencyControl(SupplierProductActivity activity,
                                                              int allocatedQuantity,
                                                              String supportedLogistics) {
        // 使用乐观锁机制更新供应商活动的库存使用情况和状态
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // 重新获取最新的活动数据，确保数据一致性
                SupplierProductActivity latestActivity = supplierProductActivityService.getById(activity.getId());
                if (latestActivity == null) {
                    throw new RuntimeException("供应商活动不存在");
                }

                // 更新已使用库存数量
                if (SupportedLogisticsEnum.PickUpOnly.name().equals(supportedLogistics)) {
                    latestActivity.setPickupLockedUsed(latestActivity.getPickupLockedUsed() + allocatedQuantity);
                } else if (SupportedLogisticsEnum.DropShippingOnly.name().equals(supportedLogistics)) {
                    latestActivity.setDropShippingLockedUsed(latestActivity.getDropShippingLockedUsed() + allocatedQuantity);
                }

                // 更新押金总额
                latestActivity.setDepositPaidTotal(activity.getDepositPaidTotal());

                // 设置活动状态
                latestActivity.setActivityState(ProductActivityStateEnum.InProgress.name());

                // 检查是否售罄（基于最新数据）
                int totalLocked = latestActivity.getPickupQuantityLocked() + latestActivity.getDropShippingQuantityLocked();
                int totalUsed = latestActivity.getPickupLockedUsed() + latestActivity.getDropShippingLockedUsed();
                if (totalLocked - totalUsed <= 0) {
                    latestActivity.setActivityState(ProductActivityStateEnum.SoldOut.name());
                    log.info("供应商活动已售罄: 活动编码={}, 总锁定={}, 总使用={}",
                        latestActivity.getSupplierActivityCode(), totalLocked, totalUsed);
                }

                // 使用乐观锁更新，如果版本冲突会抛出异常
                boolean updateSuccess = supplierProductActivityService.updateById(latestActivity);
                if (updateSuccess) {
                    // 更新成功，同步到传入的activity对象
                    activity.setPickupLockedUsed(latestActivity.getPickupLockedUsed());
                    activity.setDropShippingLockedUsed(latestActivity.getDropShippingLockedUsed());
                    activity.setActivityState(latestActivity.getActivityState());
                    activity.setDepositPaidTotal(latestActivity.getDepositPaidTotal());

                    log.info("供应商活动状态更新成功: 活动编码={}, 状态={}, 重试次数={}",
                        latestActivity.getSupplierActivityCode(), latestActivity.getActivityState(), retryCount);
                    return;
                } else {
                    throw new RuntimeException("更新失败，可能存在并发冲突");
                }

            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    log.error("供应商活动状态更新失败，已达到最大重试次数: 活动编码={}, 重试次数={}",
                        activity.getSupplierActivityCode(), retryCount, e);
                    throw new RuntimeException("供应商活动状态更新失败: " + e.getMessage(), e);
                }

                log.warn("供应商活动状态更新冲突，准备重试: 活动编码={}, 重试次数={}",
                    activity.getSupplierActivityCode(), retryCount);

                // 短暂等待后重试
                try {
                    Thread.sleep(50 * retryCount);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("线程被中断", ie);
                }
            }
        }
    }

    /**
     * 回退分销商活动库存到供应商活动（带事务保护）
     * @param distributorActivityId 分销商活动ID
     * @param supplierActivityCode 供应商活动编码
     */
    @Transactional(rollbackFor = Exception.class)
    public void rollbackDistributorStockToSupplier(Long distributorActivityId, String supplierActivityCode) {
        log.info("开始回退分销商活动库存: distributorActivityId={}, supplierActivityCode={}",
            distributorActivityId, supplierActivityCode);

        // 1. 获取分销商活动库存列表
        List<DistributorProductActivityStock> distributorStocks =
            distributorProductActivityStockService.queryListByDistributorProductActivityIds(List.of(distributorActivityId));

        if (CollUtil.isEmpty(distributorStocks)) {
            log.info("分销商活动无库存记录，跳过回退");
            return;
        }

        // 2. 获取供应商活动信息
        SupplierProductActivity supplierActivity = supplierProductActivityService.getByActivityCode(supplierActivityCode);
        if (ObjectUtil.isNull(supplierActivity)) {
            throw new RuntimeException("供应商活动不存在: " + supplierActivityCode);
        }

        // 3. 批量处理库存回退
        List<SupplierProductActivityStock> supplierStocksToUpdate = new ArrayList<>();
        int totalPickupRollback = 0;
        int totalDropShippingRollback = 0;

        for (DistributorProductActivityStock distributorStock : distributorStocks) {
            if (distributorStock.getQuantitySurplus() > 0) {
                // 获取对应的供应商库存记录
                SupplierProductActivityStock supplierStock =
                    supplierProductActivityStockService.getById(distributorStock.getSupplierActivityStockId());

                if (ObjectUtil.isNotNull(supplierStock)) {
                    // 更新供应商库存数据
                    supplierStock.setQuantitySold(supplierStock.getQuantitySold() - distributorStock.getQuantitySurplus());
                    supplierStock.setQuantitySurplus(supplierStock.getQuantitySurplus() + distributorStock.getQuantitySurplus());
                    supplierStocksToUpdate.add(supplierStock);

                    // 累计需要回退的数量
                    if (SupportedLogisticsEnum.PickUpOnly.name().equals(distributorStock.getSupportedLogistics())) {
                        totalPickupRollback += distributorStock.getQuantitySurplus();
                    } else if (SupportedLogisticsEnum.DropShippingOnly.name().equals(distributorStock.getSupportedLogistics())) {
                        totalDropShippingRollback += distributorStock.getQuantitySurplus();
                    }

                    log.info("准备回退库存: 仓库={}, 数量={}, 物流方式={}",
                        distributorStock.getWarehouseCode(),
                        distributorStock.getQuantitySurplus(),
                        distributorStock.getSupportedLogistics());
                }
            }
        }

        // 4. 批量更新供应商库存（确保原子性）
        if (!supplierStocksToUpdate.isEmpty()) {
            supplierProductActivityStockService.updateBatchById(supplierStocksToUpdate);
            log.info("批量更新供应商库存成功，共更新{}条记录", supplierStocksToUpdate.size());
        }

        // 5. 更新供应商活动的已使用数量
        if (totalPickupRollback > 0 || totalDropShippingRollback > 0) {
            supplierActivity.setPickupLockedUsed(supplierActivity.getPickupLockedUsed() - totalPickupRollback);
            supplierActivity.setDropShippingLockedUsed(supplierActivity.getDropShippingLockedUsed() - totalDropShippingRollback);
            supplierProductActivityService.updateById(supplierActivity);

            log.info("供应商活动库存回退完成: 自提回退={}, 代发回退={}", totalPickupRollback, totalDropShippingRollback);
        }
    }

    public List<SkuStock> getSupplierSkuStockByProductSkuCode(String productSkuCode, String site) {
        return productSkuStockService.getBaseMapper()
                                     .getSupplierSkuStockByProductSkuCode(productSkuCode, site);
    }

    /**
     * 供应商活动提交审核
     * @param suppLierActiveCode 供应商活动编码
     */
    public void submitForReview(String suppLierActiveCode) {
        SupplierProductActivity activity = supplierProductActivityService.getByActivityCode(suppLierActiveCode);
        if (ObjectUtil.isEmpty(activity)){
            throw new RuntimeException("活动不存在");
        }

        List<String> list = List.of(ProductActivityStateEnum.Draft.name(), ProductActivityStateEnum.NotApproved.name());
        if (!list.contains(activity.getActivityState())){
            throw new RuntimeException("只支持草稿和未通过审核的状态提交审核");
        }
        activity.setActivityState(ProductActivityStateEnum.PendingReview.name());
        supplierProductActivityService.updateById(activity);
    }

    public void pullProductSkuStockTest(String productSkuCode, String json) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        try {
            if (ObjectUtil.isEmpty(productSku)) {
                throw new ThebizarkException(1001, "商品不存在" + productSkuCode);
            }
            String supplierId = productSku.getTenantId();
            if (StrUtil.isBlank(supplierId)) {
                throw new ThebizarkException(1001, "供货商租户编号为空");
            }
            String erpSku = productSku.getSku();
            if (StrUtil.isBlank(erpSku)) {
                throw new ThebizarkException(1001, "SKU不存在");
            }

            LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductSkuStock::getProductSkuCode, productSkuCode);
            lqw.eq(ProductSkuStock::getTenantId, productSku.getTenantId());
            List<ProductSkuStock> stocks = TenantHelper.ignore(() -> iProductSkuStockService.getBaseMapper()
                                                                                            .selectList(lqw));
            Map<String, ProductSkuStock> stockMap = stocks.stream()
                                                                       .collect(Collectors.toMap(
                                                                           stock -> stock.getProductSkuCode() + stock.getWarehouseSystemCode(),
                                                                           Function.identity(),
                                                                           (existing, replacement) -> replacement
                                                                       ));

            //查询商品的product_sku_code信息查询所属的仓库】
            List<Warehouse> warehouseListByProductSkuCode =   TenantHelper.ignore(()->iWarehouseService.getWarehouseByProductSkuCode(productSkuCode));
            log.info(productSkuCode+" 商品所属仓库" + JSON.toJSONString(warehouseListByProductSkuCode));
            if (ObjectUtil.isEmpty(warehouseListByProductSkuCode)) {
                throw new ThebizarkException(101309, "商品所属的仓库信息不存在,商品编码：" + productSkuCode);
            }
            //商品唯一码关联的仓库
            Map<String, Warehouse> warehouseMap = warehouseListByProductSkuCode.stream()
                                                                               .collect(Collectors.toMap(Warehouse::getWarehouseSystemCode, Function.identity()));
            erpSku = productSku.getSku().replace(" ", "");
            List<String> erpSkuList;
            if (StrUtil.isBlank(erpSku)) {
                erpSkuList = CollUtil.newArrayList(productSku.getSku());
            } else {
                erpSkuList = StrUtil.split(erpSku, ";");
            }

            List<OutStock> outStockList = null;

            log.info("恒健仓库【查询库存】 supplierId = {} erpSkuList = {}", supplierId, JSONUtil.toJsonStr(erpSkuList));
            JSONObject result = JSONUtil.parseObj(json);

            log.info("恒健仓库【查询库存】 查询库存结果 = {}", JSONUtil.toJsonStr(result));
            if (result.getInt("statusCode") != null && result.getInt("statusCode") == 200) {
                String date = result.getStr("data");
                outStockList = JSONUtil.toList(JSONUtil.parseArray(date), OutStock.class);
            }
            if (CollUtil.isNotEmpty(outStockList)) {
                // 根据恒健仓库的文档筛选出可售库存
                outStockList = outStockList.stream().filter(outStock -> outStock.getInventoryType() == 0)
                                           .collect(Collectors.toList());
                //转为Map
                //根据warehouseCode 聚合去重
                HashMap<String, OutStock> outStockHashMap = groupByWarehouseCode(outStockList);


                // 后续 产品梳理做planB
                //   String code = "EFCA";
                //商品所属的仓库
                for (String code : warehouseMap.keySet()) {
//                    if (!ObjectUtil.equals("GA296933",code)){
//                        continue;
//                    }
                    OutStock outStock = outStockHashMap.get(code);
                    Warehouse warehouse = warehouseMap.get(code);
                    ProductSkuStock productSkuStock = stockMap.get(productSku.getProductSkuCode() + warehouse.getWarehouseSystemCode());

                    if (ObjectUtil.isEmpty(outStock)) {
                        // 如果没有匹配到外部库存数据，将库存设置为0
                        log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 未匹配到外部库存，设置库存为0", productSkuCode, code);
                        iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), 0, null);
                        productSupport.updateProductInventoryPushTime(productSkuCode);
                        // 因为是具体仓库现在没有库存，根据商品+仓库 匹配分销商活动
                        if (ObjectUtil.isNotNull(productSkuStock)){
                            updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuStock.getId());
                            if (productSkuStock.getPickupLockUsed()>0 || productSkuStock.getDropShippingLockUsed()>0){
                                //当前商品参与了活动，将改库存设置为异常
                                productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                            }
                        }
                    }else {
                        // 如果匹配到外部库存数据，使用外部库存数量
                        int  totalInventory = outStock.getInventoryAvailable();
                        //是否单仓
                        String singleWarehouseFlag = outStock.getSingleWarehouseFlag();
                        int sw = ObjectUtil.equal("true", singleWarehouseFlag) ? 0 : 1;
                        //判断数量,获取库存信息，这里采用

                        if (ObjectUtil.isNotEmpty(productSkuStock)){
                            //如果是单仓
                            if (sw == 0){
                                //先判断当前仓库有没有代发模式的锁货活动，如果有将供应商/分销商活动设置为异常，库存不更新
                                if (productSkuStock.getDropShippingLockUsed()>0){
                                    log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 有代发锁货活动，设置库存为异常", productSkuCode, code);
                                    updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuStock.getId());
                                    productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                    TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                }else {
                                    if (totalInventory-productSkuStock.getPickupLockUsed() > 0){
                                        log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 最新ERP推送数量 = {}", productSkuCode, code, totalInventory);
                                        //根据商品SKU唯一编号和仓库编号更新库存
                                        iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), totalInventory-productSkuStock.getPickupLockUsed(), sw);
                                        //更新product表的库存推送时间
                                        productSupport.updateProductInventoryPushTime(productSkuCode);
                                        //将这个商品关联的所有活动设置为正常
                                        updateProductActivityStockException(1,productSkuStock.getId());
                                        productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
                                        TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                    }else {
                                        log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 有自提锁货活动，设置库存为异常", productSkuCode, code);
                                        updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(), productSkuStock.getId());
                                        productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                        TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                    }
                                }

                            }
                            //非单仓
                            if (sw == 1){
                                if (totalInventory-productSkuStock.getPickupLockUsed()-productSkuStock.getDropShippingLockUsed() > 0){
                                    log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 最新ERP推送数量 = {}", productSkuCode, code, totalInventory);
                                    //根据商品SKU唯一编号和仓库编号更新库存
                                    iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), totalInventory-productSkuStock.getPickupLockUsed()-productSkuStock.getDropShippingLockUsed(), sw);
                                    //更新product表的库存推送时间
                                    productSupport.updateProductInventoryPushTime(productSkuCode);
                                    //将这个商品关联的所有活动设置为正常
                                    updateProductActivityStockException(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode(),productSkuStock.getId());
                                    productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
                                    TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                }else {
                                    updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuStock.getId());
                                    productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                    TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                }
                            }
                        }
                    }
                }
                updateProductActivityExceptionByStock(productSkuCode);
            }else {
                log.info("恒健仓库【查询库存】 查询库存结果为空");
                //将当前品所属的仓库数量全部更新为0
                warehouseMap.forEach((key, warehouse) -> {
                    //根据商品SKU唯一编号和仓库编号更新库存
                    iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), 0,null);
                    //更新product表的库存推送时间
                    productSupport.updateProductInventoryPushTime(productSkuCode);
                    ProductSkuStock productSkuStock = stockMap.get(productSku.getProductSkuCode() + warehouse.getWarehouseSystemCode());
                    if (ObjectUtil.isEmpty(productSkuStock)){
                        if (productSkuStock.getPickupLockUsed()>0 || productSkuStock.getDropShippingLockUsed()>0){
                            //当前商品参与了活动，将改库存设置为异常
                            productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                            TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                        }
                    }

                });
                //将这个商品关联的所有活动设置为异常
                updateProductActivityException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuCode,null);
            }
        } catch (Exception e) {
            log.error("恒健仓库【查询库存】 查询库存发生异常 {}", e.getMessage(), e);
        }

    }
    /**
     * 处理List<OutStock> 相同的warehouseCode 对InventoryAvailable相加
     *
     * @param outStockLists
     * @return
     */
    public static HashMap<String, OutStock> groupByWarehouseCode(List<OutStock> outStockLists) {
        // 使用Stream API进行操作
        Map<String, List<OutStock>> groupedByWarehouseSystemCode = outStockLists.stream()
                                                                                .collect(Collectors.groupingBy(OutStock::getWarehouseSystemCode));
        // 将Map转换回List<OutStock>，选择第一个出现的OutStock对象作为代表，并更新它的inventoryAvailable字段
        HashMap<String, OutStock> outStockMap = new HashMap<>();
        for (Map.Entry<String, List<OutStock>> entry : groupedByWarehouseSystemCode.entrySet()) {
            List<OutStock> outStockList = entry.getValue();
            OutStock representativeOutStock = outStockList.get(0);
            int totalInventoryAvailable = outStockList.stream().mapToInt(OutStock::getInventoryAvailable).sum();
            representativeOutStock.setInventoryAvailable(totalInventoryAvailable);
            outStockMap.put(entry.getKey(), representativeOutStock);
        }
        return outStockMap;
    }

    /**
     * 供应商添加活动商品列表
     * @param productSkuCode 商品编码
     * @param productSku 商品Sku
     * @param productName 商品名称
     * @param site 站点
     * @param page 分页
     * @return
     */
    public TableDataInfo<ActivityProductSkuDTO> getActivityProductSkuList(String productSkuCode, String productSku, String productName,
                                                                 String site,Page<ActivityProductSkuDTO> page) {
      String tenantId = LoginHelper.getTenantId();
      IPage<ActivityProductSkuDTO>  s= TenantHelper.ignore(()->supplierProductActivityService.getBaseMapper().
                                                                                             getActivityProductSkuList(page,productSkuCode,productSku,productName,site,tenantId) );
        List<String> collect = s.getRecords().stream()
                          .filter(Objects::nonNull)
                          .map(ActivityProductSkuDTO::getProductSkuCode)
                          .filter(Objects::nonNull)
                          .collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)){
            return TableDataInfo.build(new ArrayList<>(), 0);
        }

        List<ProductSkuAttachmentVo> vos = TenantHelper.ignore(()-> productSkuAttachmentMapper.queryFirstImageByProductSkuCodes(collect));
        Map<Long, ProductSkuAttachmentVo> productSkuAttachmentMap = vos.stream()
                                                                                           .collect(Collectors.toMap(
                                                                                               ProductSkuAttachmentVo::getProductSkuId,
                                                                                               productSkuAttachmentVo -> productSkuAttachmentVo,
                                                                                               (existing, replacement) -> existing
                                                                                           ));

        List<SkuStock> skuStocks = productSkuStockService.getBaseMapper().getSkuStocks(collect);
        Map<String, SkuStock> skuStockMap = skuStocks.stream()
                                                     .collect(Collectors.toMap(
                                                         SkuStock::getProductSkuCode,
                                                         skuStock -> skuStock,
                                                         (existing, replacement) -> existing
                                                     ));
        List<ActivityProductSkuDTO> records = s.getRecords();
        records.forEach(ss->{
            ProductSkuAttachmentVo attachmentVo = productSkuAttachmentMap.get(ss.getProductSkuId());
            if (ObjectUtil.isNotNull(attachmentVo)){
                ss.setProductSkuImg(attachmentVo.getAttachmentShowUrl());
            }
            SkuStock skuStock = skuStockMap.get(ss.getProductSkuCode());
            if (ObjectUtil.isNotNull(skuStock)){
                ss.setPickUpStock(skuStock.getPickUpStockTotal());
                ss.setDropShippingStock(skuStock.getDropShippingStockTotal());
            }
        });
        return TableDataInfo.build(records, page.getTotal());
    }

    /**
     * 库存更新记录，用于回滚操作
     */
    @Data
    public static class StockUpdateRecord {
        private final Long stockId;
        private final Integer quantity;
        private final String warehouseCode;

        public StockUpdateRecord(Long stockId, Integer quantity, String warehouseCode) {
            this.stockId = stockId;
            this.quantity = quantity;
            this.warehouseCode = warehouseCode;
        }

    }
}
