package com.zsmall.extend.shop.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.extend.shop.config.properties.ShopifyProperties;
import com.zsmall.extend.shop.constants.CacheConstants;
import com.zsmall.extend.shop.enums.DispatchType;
import com.zsmall.extend.shop.enums.ZSMallExtendShopStatusCodeEnum;
import com.zsmall.extend.shop.model.vo.ConnectChannelVo;
import com.zsmall.extend.shop.service.TpsShopifyService;
import com.zsmall.extend.shop.service.business.TpsBusinessShopifyService;
import com.zsmall.extend.shop.shopify.kit.AccessTokenKit;
import com.zsmall.extend.shop.shopify.kit.ShopifyDelegate;
import com.zsmall.extend.shop.shopify.kit.ShopifyKit;
import com.zsmall.extend.shop.shopify.model.Shop;
import com.zsmall.extend.shop.shopify.model.ShopifyClientBean;
import com.zsmall.extend.shop.shopify.model.accesstoken.AccessToken;
import com.zsmall.extend.shop.shopify.utils.HmacValidateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class TpsShopifyServiceImpl implements TpsShopifyService {

    private final ShopifyProperties shopifyProperties;
    private final ShopifyClientBean shopifyClientBean;
    private final TpsBusinessShopifyService tpsBusinessShopifyService;

    @Override
    public void authorization(HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
        try {
            String shop = "";
            Map<String, String> map = new HashMap<>();
            Enumeration<String> parameterNames = httpRequest.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String key = parameterNames.nextElement();
                String value = httpRequest.getParameter(key);
                if (StrUtil.equals("shop", key)) {
                    shop = value;
                }
                map.put(key, value);
            }
            log.info("map = {}", JSONUtil.toJsonStr(map));
            boolean isv = HmacValidateUtil.validateAskForPermission(shopifyProperties.getClientSecret(), map);
            if (isv) {
                byte[] textByte = shop.getBytes("UTF-8");
                String path = generateAuthUrl(shop);
                httpResponse.sendRedirect(path);
            } else {
                JSONObject result = JSONUtil.createObj().set("code", "1000").set("message", "认证失败");
                result.write(httpResponse.getWriter());
            }
        } catch (Exception e) {
            log.error("Shopify应用授权异常", e.getMessage(), e);
        }
    }

    @Override
    public String generateAuthUrl(String shop) {
        JSONObject jsonObject = new JSONObject();
        String key = UUID.randomUUID().toString();
        jsonObject.set("dispatchType", DispatchType.SHOPIFY_AUTHORIZE);
        jsonObject.set("random", key);
        jsonObject.set("shop", shop);

        String authCacheKey = getAuthCacheKey(key);
        RedisUtils.setCacheMap(authCacheKey, jsonObject);
        RedisUtils.expire(authCacheKey, Duration.ofSeconds(CacheConstants.AUTH_EXPIRATION));

        String params = Base64.encode(jsonObject.toString());
        String path = String.format(shopifyProperties.getAuthorizeUrl(), shop, shopifyProperties.getClientKey(),
            shopifyProperties.getGrantOptions(), shopifyProperties.getRedirectUri(), shopifyProperties.getScope(), params);
        return path;
    }

    @NotNull
    private static String getAuthCacheKey(String key) {
        return CacheConstants.KEY_SHOPIFY_AUTH + ":" + key;
    }

    @Override
    public ConnectChannelVo authorizeRedirect(JSONObject requestBody, HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws RStatusCodeException {
        log.info("调用【Shopify应用授权重定向】接口，接口请求参数：{} ", JSONUtil.toJsonStr(requestBody));

        String state = requestBody.getStr("state");
        String code = requestBody.getStr("code");
        String shop = requestBody.getStr("shop");

        // 自定义参数state处理
        String params = StrUtil.str(Base64.decode(state), StandardCharsets.UTF_8);
        JSONObject jsonObject = JSONUtil.parseObj(params);
        log.info("jsonObject = {}", jsonObject.toString());
        // 随机码
        String random = jsonObject.getStr("random");
        String redisKey = getAuthCacheKey(random);
        // redis中存的另一个随机码
        String random2 = RedisUtils.getCacheMapValue(redisKey, "random");

        log.info("random = {}", random);
        log.info("random2 = {}", random2);

        // 两个随机码必须一致才能继续授权认证，防止重复认证
        if (StringUtils.isBlank(random) || StringUtils.isBlank(random2) || !StringUtils.equals(random, random2)) {
//            throw new RStatusCodeException(ZSMallExtendShopStatusCodeEnum.SHOPIFY_AUTHENTICATION_EXPIRED);
        }
        RedisUtils.deleteObject(redisKey);

        // 许可认证
        Map<String, String> map = new HashMap<>();
        requestBody.forEach((key, value) -> {
            map.put(key, requestBody.getStr(key));
        });
//        log.info("map = {}", JSONUtil.toJsonStr(map));
        boolean isv = HmacValidateUtil.validateAskForPermission(shopifyProperties.getClientSecret(), map);
        if (!isv) {
            throw new RStatusCodeException(ZSMallExtendShopStatusCodeEnum.SHOPIFY_AUTHENTICATION_EXPIRED);
        }

//        String shopString = "{\"address2\":\"\",\"checkout_api_supported\":false,\"country\":\"CN\",\"country_code\":\"CN\",\"country_name\":\"China\",\"county_taxes\":true,\"created_at\":1684225969000,\"customer_email\":\"<EMAIL>\",\"currency\":\"CNY\",\"domain\":\"wezone-shop-test-319.myshopify.com\",\"enabled_presentment_currencies\":[\"CNY\"],\"eligible_for_card_reader_giveaway\":true,\"eligible_for_payments\":false,\"email\":\"<EMAIL>\",\"force_ssl\":true,\"has_discounts\":false,\"has_gift_cards\":false,\"has_storefront\":true,\"iana_timezone\":\"America/New_York\",\"id\":76819824952,\"money_format\":\"¥{{amount}}\",\"money_in_emails_format\":\"¥{{amount}}\",\"money_with_currency_format\":\"¥{{amount}} CNY\",\"money_with_currency_in_emails_format\":\"¥{{amount}} CNY\",\"multi_location_enabled\":true,\"myshopify_domain\":\"wezone-shop-test-319.myshopify.com\",\"name\":\"wezone-shop-test-319\",\"password_enabled\":true,\"plan_display_name\":\"Developer Preview\",\"pre_launch_enabled\":false,\"cookie_consent_level\":\"implicit\",\"primary_location_id\":84135510328,\"requires_extra_payments_agreement\":false,\"setup_required\":false,\"shop_owner\":\"BeirdChampagne\",\"taxes_included\":false,\"timezone\":\"(GMT-05:00) America/New_York\",\"updated_at\":1684229107000,\"weight_unit\":\"kg\"}";
//        String tokenString = "{\"access_token\":\"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\",\"scope\":\"write_product_listings,write_orders,write_products,write_third_party_fulfillment_orders,write_shipping,write_translations,write_fulfillments,write_inventory\",\"shop\":\"wezone-shop-test-319.myshopify.com\"}";
//        AccessToken accessToken = JSONUtil.toBean(tokenString, AccessToken.class);
//        Shop shopifyShop = JSONUtil.toBean(shopString, Shop.class);

        // 获取AccessToken
        ShopifyDelegate shopifyDelegate = ShopifyKit.create();
        AccessToken accessToken = shopifyDelegate.accessTokenApi().getAccessToken(shop, code);
        AccessTokenKit.put(shop, accessToken);
        // 获取Shopify商店信息
        Shop shopifyShop = shopifyDelegate.shopApi(shop).getShopifyShop();
        log.info("authorizeRedirect shopifyShop = {}", JSONUtil.toJsonStr(shopifyShop));
        String email = shopifyShop.getEmail();

        // 业务系统与Shopify账号绑定
        ConnectChannelVo respBody = tpsBusinessShopifyService.bindAccount(shopifyShop, accessToken);


        return respBody;
    }
}
