package com.zsmall.extend.shop.amazon.business.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.extend.shop.amazon.business.entity.domain.AmazonAppConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 亚马逊应用管理业务对象 amazon_app_config
 *
 * <AUTHOR> Li
 * @date 2023-10-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AmazonAppConfig.class, reverseConvertGenerate = false)
public class AmazonAppConfigBo extends BaseEntity {

    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 应用Id
     */
    @NotBlank(message = "应用Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appId;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appName;

    /**
     * 应用请求地址
     */
    @NotBlank(message = "应用请求地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endpoint;

    /**
     * 应用授权信息
     */
    @NotBlank(message = "应用授权信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String authorizeInfo;

    /**
     * 已授权数量
     */
    private Integer authorizedQuantity;

    /**
     * 应用状态
     */
    private Integer appStatus;

    /**
     * 排序号
     */
    private Integer sortNum;


}
