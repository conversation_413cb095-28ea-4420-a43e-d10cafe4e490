package com.zsmall.extend.shop.amazon.business.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.extend.shop.amazon.business.entity.domain.AmazonAppConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 亚马逊应用管理视图对象 amazon_app_config
 *
 * <AUTHOR> Li
 * @date 2023-10-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AmazonAppConfig.class)
public class AmazonAppConfigDetailVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private String id;

    /**
     * 应用Id
     */
    @ExcelProperty(value = "应用Id")
    private String appId;

    /**
     * 应用名称
     */
    @ExcelProperty(value = "应用名称")
    private String appName;

    /**
     * 应用请求地址
     */
    private String endpoint;

    /**
     * 应用授权信息
     */
    private String authorizeInfo;

    /**
     * 已授权数量
     */
    @ExcelProperty(value = "已授权数量")
    private Integer authorizedQuantity;

    /**
     * 应用状态
     */
    @ExcelProperty(value = "应用状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "amazon_app_status")
    private Integer appStatus;

    /**
     * 排序号
     */
    @ExcelProperty(value = "排序号")
    private Integer sortNum;


}
