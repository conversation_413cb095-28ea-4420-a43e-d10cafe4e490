package com.zsmall.extend.shop.amazon.business.entity.iservice.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.extend.shop.amazon.business.constants.AmazonCaches;
import com.zsmall.extend.shop.amazon.business.entity.domain.AmazonAppConfig;
import com.zsmall.extend.shop.amazon.business.entity.domain.bo.AmazonAppConfigBo;
import com.zsmall.extend.shop.amazon.business.entity.domain.vo.AmazonAppConfigDetailVo;
import com.zsmall.extend.shop.amazon.business.entity.domain.vo.AmazonAppConfigVo;
import com.zsmall.extend.shop.amazon.business.entity.iservice.IAmazonAppConfigService;
import com.zsmall.extend.shop.amazon.business.entity.mapper.AmazonAppConfigMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 亚马逊应用管理Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-10-24
 */
@RequiredArgsConstructor
@Service
public class AmazonAppConfigServiceImpl implements IAmazonAppConfigService {

    private final AmazonAppConfigMapper baseMapper;

    /**
     * 查询亚马逊应用管理
     */
    @Override
    public AmazonAppConfigDetailVo queryById(String id){
        AmazonAppConfig amazonAppConfig = baseMapper.selectById(id);
        AmazonAppConfigDetailVo detailVo = null;
        if(amazonAppConfig != null) {
            detailVo = MapstructUtils.convert(amazonAppConfig, AmazonAppConfigDetailVo.class);
        }
        return detailVo;
    }

    /**
     * 查询亚马逊应用管理列表
     */
    @Override
    public TableDataInfo<AmazonAppConfigVo> queryPageList(AmazonAppConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AmazonAppConfig> lqw = buildQueryWrapper(bo);
        Page<AmazonAppConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询亚马逊应用管理列表
     */
    @Override
    public List<AmazonAppConfigVo> queryList(AmazonAppConfigBo bo) {
        LambdaQueryWrapper<AmazonAppConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AmazonAppConfig> buildQueryWrapper(AmazonAppConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AmazonAppConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), AmazonAppConfig::getAppId, bo.getAppId());
        lqw.like(StringUtils.isNotBlank(bo.getAppName()), AmazonAppConfig::getAppName, bo.getAppName());
        lqw.eq(bo.getAppStatus() != null, AmazonAppConfig::getAppStatus, bo.getAppStatus());

        lqw.orderByAsc(NoDeptBaseEntity::getCreateTime).orderByAsc(AmazonAppConfig::getId);
        return lqw;
    }

    /**
     * 新增亚马逊应用管理
     */
    @Override
    public Boolean insertByBo(AmazonAppConfigBo bo) {
        AmazonAppConfig add = MapstructUtils.convert(bo, AmazonAppConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
//        if (flag) {
//            bo.setId(add.getId());
//        }
        return flag;
    }

    /**
     * 修改亚马逊应用管理
     */
    @CachePut(cacheNames = AmazonCaches.AMAZON_APP_KEY, key = "#bo.id")
    @Override
    public Boolean updateByBo(AmazonAppConfigBo bo) {
        AmazonAppConfig update = MapstructUtils.convert(bo, AmazonAppConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AmazonAppConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除亚马逊应用管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取可用的app 主键
     *
     * @return
     */
    @Override
    public String getEnableApp() {
        LambdaQueryWrapper<AmazonAppConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(AmazonAppConfig::getAppStatus, 1);
        lqw.orderByAsc(NoDeptBaseEntity::getCreateTime).orderByAsc(AmazonAppConfig::getId);

        // 只取第一条
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(1);
        Page<AmazonAppConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        List<AmazonAppConfigVo> records = result.getRecords();

        if(CollUtil.isNotEmpty(records)) {
            AmazonAppConfigVo amazonAppConfigVo = records.get(0);
            return amazonAppConfigVo.getId();
        }

        return null;
    }

    /**
     * 根据主键查询应用配置
     *
     * @param id
     * @return
     */
    @Cacheable(cacheNames = AmazonCaches.AMAZON_APP_KEY, key = "#id")
    @Override
    public AmazonAppConfig selectConfigById(String id) {
        return baseMapper.selectById(id);
    }

    /**
     * 根据应用Id查询应用配置
     *
     * @param appId
     * @return
     */
    @Override
    public AmazonAppConfig selectConfigByAppId(String appId) {
        LambdaQueryWrapper<AmazonAppConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(AmazonAppConfig::getAppId, appId);
        return baseMapper.selectOne(lqw);
    }
}
