package com.zsmall.extend.utils;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.json.JSONArray;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.exception.StockException;
import com.zsmall.extend.event.activity.ActivityExpirationNoticeEvent;
import com.zsmall.extend.event.activity.ActivityStockAdjustEvent;

import java.lang.reflect.UndeclaredThrowableException;

/**
 * ZSMall-活动相关事件工具
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
public class ZSMallActivityEventUtils {

    /**
     * 调整活动库存
     * @param dto
     * @return
     */
    public static String activityStockAdjust(AdjustStockDTO dto) throws StockException {
        ActivityStockAdjustEvent event = new ActivityStockAdjustEvent();
        event.setDto(dto);

        try {
            SpringUtils.context().publishEvent(event);
        } catch (UndeclaredThrowableException e) {
            Throwable undeclaredThrowable = e.getUndeclaredThrowable();
            if (undeclaredThrowable instanceof StockException) {
                throw (StockException)undeclaredThrowable;
            }
        }

        return event.getWarehouseSystemCode();
    }

    public static JSONArray activityExpirationNotice() {
        ActivityExpirationNoticeEvent event = new ActivityExpirationNoticeEvent(StpUtil.getTokenValue(), LoginHelper.getTenantId());
        SpringUtils.context().publishEvent(event);
        return event.getMessageArray();
    }
}
