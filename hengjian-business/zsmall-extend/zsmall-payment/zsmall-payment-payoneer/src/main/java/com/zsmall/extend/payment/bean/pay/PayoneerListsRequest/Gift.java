package com.zsmall.extend.payment.bean.pay.PayoneerListsRequest;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class Gift implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long amount;
    private String currency;
    private Long cardCount;
}
