package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单日志表
 * @TableName bill_log
 */
@TableName(value ="bill_log")
@Data
@EqualsAndHashCode(callSuper=false)
public class BillLog extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 原账单表id
     */
    private Long originBillId;

    /**
     * 上一账单id
     */
    private Long previousBillId;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 结算时间
     */
    private Date settlementDateTime;

    /**
     * 结算周期开始时间
     */
    private Date settlementCycleBegin;

    /**
     * 结算周期截止时间
     */
    private Date settlementCycleEnd;

    /**
     * 本期收入
     */
    private BigDecimal currentIncome;

    /**
     * 本期支出
     */
    private BigDecimal currentExpenditure;

    /**
     * 本期循环保证金
     */
    private BigDecimal currentCircularDeposit;

    /**
     * 上期循环保证金
     */
    private BigDecimal previousCircularDeposit;

    /**
     * 本期总金额
     */
    private BigDecimal currentTotalAmount;

    /**
     * 账单状态（0-未结算，1-已结算）
     */
    private Integer billState;

    /**
     * 生成状态（0-未生成，10-生成中，20-已生成，30-生成失败）
     */
    private Integer generateState;

    /**
     * 提现状态（0-提现，1未提现）
     */
    private Integer withdrawalState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    private static final long serialVersionUID = 1L;
}
