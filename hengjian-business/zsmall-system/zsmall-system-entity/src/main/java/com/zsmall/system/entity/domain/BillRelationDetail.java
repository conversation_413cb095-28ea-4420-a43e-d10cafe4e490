package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.bill.RelationFieldTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 账单关系详情表
 * @TableName bill_relation_detail
 */
@TableName(value ="bill_relation_detail")
@Data
@EqualsAndHashCode(callSuper=false)
public class BillRelationDetail extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 账单关系id
     */
    private Long billRelationId;

    /**
     * 详情字段类型（ItemNo、产品金额、操作费等可扩展字段）
     */
    private RelationFieldTypeEnum fieldType;

    /**
     * 详情字段值
     */
    private String fieldValue;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    private static final long serialVersionUID = 1L;
}
