package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.transaction.ReceiptReviewStateEnum;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 交易回执单对象 transaction_receipt
 *
 * <AUTHOR> Li
 * @date 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("transaction_receipt")
public class TransactionReceipt extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 操作人用户编号（员工审核时记录）
     */
    private String operatorTenantId;

    /**
     * 交易记录主键（产生交易时才会存）
     */
    private Long transactionsId;

    /**
     * 交易回执单编号（内部使用，由系统生成）
     */
    private String transactionReceiptNo;

    /**
     * 交易类型（Recharge-充值，Withdrawal-提现）
     */
    private TransactionTypeEnum transactionType;

    /**
     * 交易方式
     */
    private TransactionMethodEnum transactionMethod;

    /**
     * 交易金额
     */
    private BigDecimal transactionAmount;

    /**
     * 交易手续费
     */
    private BigDecimal transactionFee;

    /**
     * 实际交易时间
     */
    private LocalDateTime transactionTime;

    /**
     * 到账时间（员工批准提现时间）
     */
    private LocalDateTime receiptTime;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 收款账户ID
     */
    private Long receiptAccountId;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 快捷代码
     */
    private String swiftCode;

    /**
     * 备注
     */
    private String note;

    /**
     * 备注（员工）
     */
    private String noteManager;

    /**
     * 第三方交易渠道单号
     */
    private String thirdChannelNo;

    /**
     * 第三方交易渠道账号
     */
    private String thirdChannelAccount;

    /**
     * 审核状态
     */
    private ReceiptReviewStateEnum reviewState;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
