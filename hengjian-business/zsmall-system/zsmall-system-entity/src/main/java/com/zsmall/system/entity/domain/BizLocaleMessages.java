package com.zsmall.system.entity.domain;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;



/**
 * 翻译消息数据对象 biz_locale_messages
 *
 * <AUTHOR> Li
 * @date 2024-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_locale_messages")
public class BizLocaleMessages extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 所属子系统id
     */
    private Long systemId;

    /**
     * 所属语言包标识
     */
    private String locale;

    /**
     * 所属标识生成的lange_key,格式:[site].[package].[model].[group].key
     */
    private String langKey;

    /**
     * 所属语言翻译值数据
     */
    private String langValue;

    /**
     * 标识描述
     */
    private String langKeyDesc;

    /**
     * 操作时间
     */
//    private Date opAt;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 删除时间
     */
    private Date delTime;
}
