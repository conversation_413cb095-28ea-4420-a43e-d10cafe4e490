package com.zsmall.system.entity.domain.vo.transaction;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.system.entity.domain.TransactionReceipt;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 交易回执单视图对象 transaction_receipt
 *
 * <AUTHOR> Li
 * @date 2023-06-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TransactionReceipt.class)
public class TransactionReceiptVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
//    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 操作人用户编号（员工审核时记录）
     */
//    @ExcelProperty(value = "操作人用户编号", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(readConverterExp = "员工审核时记录")
    private String operatorTenantId;

    /**
     * 租户Id/分销商
     */
    @ExcelProperty(value = "分销商")
    @ExcelI18nFormat(code = "zsmall.excel.distributor")
    private String tenantId;

    /**
     * 交易记录主键（产生交易时才会存）
     */
//    @ExcelProperty(value = "交易记录主键", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(readConverterExp = "产生交易时才会存")
    private Long transactionsId;

    @ExcelProperty(value = "店铺标识")
    @ExcelI18nFormat(code = "zsmall.excel.thirdChannelFlag")
    private String thirdChannelFlag;
    /**
     * 交易回执单编号（内部使用，由系统生成）
     */
    @ExcelProperty(value = "交易回执单编号")
    @ExcelI18nFormat(code = "zsmall.excel.transactionReceiptNo")
//    @ExcelDictFormat(readConverterExp = "内部使用，由系统生成")
    private String transactionReceiptNo;

    @ExcelProperty(value = "申请时间")
    @ExcelI18nFormat(code = "zsmall.excel.applicationTime")
    private Date createTime;

    /**
     * 交易类型（Recharge-充值，Withdrawal-提现）
     */
    @ExcelProperty(value = "交易类型", converter = ExcelDictConvert.class)
    @ExcelI18nFormat(code = "zsmall.excel.transactionType")
    @ExcelDictFormat(readConverterExp = "Recharge=充值,Withdrawal=提现,Income=收入,Expenditure=支出")
    private String transactionType;

    /**
     * 交易方式
     */
    @ExcelProperty(value = "交易方式", converter = ExcelDictConvert.class)
    @ExcelI18nFormat(code = "zsmall.excel.transactionMethod")
    @ExcelDictFormat(readConverterExp = "OfflinePayoneer=线下派安盈,OnlinePayoneer=线上派安盈,DirectBankTransfer=银行直接转账")
    private String transactionMethod;

    @ExcelProperty(value = "币种")
    @ExcelI18nFormat(code = "zsmall.excel.currency")
    private String currency;

    @ExcelIgnore
    private String currencySymbol;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    @ExcelI18nFormat(code = "zsmall.excel.transactionAmount")
    private BigDecimal transactionAmount;

    /**
     * 交易手续费
     */
    @ExcelProperty(value = "交易手续费")
    @ExcelI18nFormat(code = "zsmall.excel.transactionFee")
    private BigDecimal transactionFee;

    /**
     * 实际交易时间
     */
    @ExcelProperty(value = "汇款时间")
    @ExcelI18nFormat(code = "zsmall.excel.transactionTime")
    private LocalDateTime transactionTime;

    /**
     * 到账时间（员工批准提现时间）
     */
    @ExcelProperty(value = "到账时间")
    @ExcelI18nFormat(code = "zsmall.excel.receiptTime")
//    @ExcelDictFormat(readConverterExp = "员工批准提现时间")
    private LocalDateTime receiptTime;

    /**
     * 账户ID
     */
    @ExcelProperty(value = "账户ID")
    @ExcelI18nFormat(code = "zsmall.excel.accountId")
    private String accountId;

    /**
     * 账户名称
     */
    @ExcelProperty(value = "账户名称")
    @ExcelI18nFormat(code = "zsmall.excel.accountName")
    private String accountName;

    /**
     * 收款账户ID
     */
//    @ExcelProperty(value = "收款账户ID")
    private Long receiptAccountId;

    /**
     * 银行名称
     */
//    @ExcelProperty(value = "银行名称")
    private String bankName;

    /**
     * 快捷代码
     */
//    @ExcelProperty(value = "快捷代码")
    private String swiftCode;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ExcelI18nFormat(code = "zsmall.excel.note")
    private String note;

    /**
     * 备注（员工）
     */
//    @ExcelProperty(value = "备注", converter = ExcelDictConvert.class)
//    @ExcelDictFormat(readConverterExp = "员工")
    private String noteManager;

    /**
     * 第三方交易渠道单号
     */
//    @ExcelProperty(value = "第三方交易渠道单号")
    private String thirdChannelNo;

    /**
     * 第三方交易渠道账号
     */
//    @ExcelProperty(value = "第三方交易渠道账号")
    private String thirdChannelAccount;

    /**
     * 审核状态
     */
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelI18nFormat(code = "zsmall.excel.reviewState")
    @ExcelDictFormat(readConverterExp = "Pending=待审批,Accepted=已到账,Rejected=已拒绝")
    private String reviewState;

}
