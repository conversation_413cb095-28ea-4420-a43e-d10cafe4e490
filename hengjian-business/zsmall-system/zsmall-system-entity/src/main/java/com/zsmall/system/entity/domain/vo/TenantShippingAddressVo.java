package com.zsmall.system.entity.domain.vo;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.system.entity.domain.TenantShippingAddress;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 用户收货地址视图对象 tenant_shipping_address
 *
 * <AUTHOR>
 * @date 2023-08-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TenantShippingAddress.class)
public class TenantShippingAddressVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 手机号码国际区号
     */
    @ExcelProperty(value = "手机号码国际区号")
    private String areaCode;

    /**
     * 默认状态
     */
    @ExcelProperty(value = "默认状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
    private Integer defaultState;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     * 全名
     */
    @ExcelProperty(value = "全名")
    private String fullName;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 收货地址
     */
    @ExcelProperty(value = "收货地址")
    private String shippingAddress;

    /**
     * 收货地址（国际化）
     */
    @ExcelProperty(value = "收货地址（国际化）")
    private JSONObject shippingAddressLocale;

    /**
     * 国家代码
     */
    @ExcelProperty(value = "国家代码")
    private String countryCode;

    /**
     * 省/州代码
     */
    @ExcelProperty(value = "省/州代码")
    private String stateCode;

    /**
     * 国家代码
     */
    @ExcelProperty(value = "国家代码")
    private Long countryId;

    /**
     * 省/州代码
     */
    @ExcelProperty(value = "省/州代码")
    private Long stateId;

    private String state;

    /**
     * 城市
     */
    @ExcelProperty(value = "城市")
    private String city;

    /**
     * 详细地址1
     */
    @ExcelProperty(value = "详细地址1")
    private String address1;

    /**
     * 详细地址2
     */
    @ExcelProperty(value = "详细地址2")
    private String address2;

    /**
     * 详细地址2
     */
    @ExcelProperty(value = "详细地址3")
    private String address3;

    /**
     * zipCode
     */
    @ExcelProperty(value = "zipCode")
    private String zipCode;

    /**
     * 是否使用当前手机号
     */
    @ExcelProperty(value = "是否使用当前手机号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
    private Long isSameMember;


}
