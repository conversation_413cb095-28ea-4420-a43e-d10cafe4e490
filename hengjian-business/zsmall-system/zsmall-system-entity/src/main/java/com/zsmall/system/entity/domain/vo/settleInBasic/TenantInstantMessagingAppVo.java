package com.zsmall.system.entity.domain.vo.settleInBasic;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.TenantInstantMessagingApp;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 用户联系方式APP视图对象 tenant_instant_messaging_app
 *
 * <AUTHOR> Li
 * @date 2023-08-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TenantInstantMessagingApp.class)
public class TenantInstantMessagingAppVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 联系方式APP
     */
    @ExcelProperty(value = "联系方式APP")
    private String messagingAppType;

    /**
     * 联系方式Id
     */
    @ExcelProperty(value = "联系方式Id")
    private String messagingAppNumber;


}
