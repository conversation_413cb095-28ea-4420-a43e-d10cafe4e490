package com.zsmall.system.entity.domain.vo.billTransactionReceipt;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.system.entity.domain.BillTransactionReceipt;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 钱包账单视图对象 bill_transaction_receipt
 *
 * <AUTHOR> Li
 * @date 2024-09-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BillTransactionReceipt.class)
public class BillTransactionReceiptVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelIgnore
    private Long id;
    /**
     * 租户ID
     */
    @ExcelProperty(value = "分销商ID")
    private String tenantId;
    /**
     * 钱包账单编号
     */
    @ExcelProperty(value = "账单编号")
    private String billNo;

    /**
     * 账单开始时间
     */

    @ExcelIgnore
    private Date billStartTime;

    /**
     * 账单结束时间
     */

    @ExcelIgnore
    private Date billEndTime;
    @ExcelProperty(value = "账单周期")
    private String billTime;

    @ExcelProperty(value = "币种")
    private String currencyCode;
    /**
     * 月初钱包余额
     */
    @ExcelProperty(value = "月初钱包余额")
    private BigDecimal walletBalanceMonthStart;
    /**
     * 当月钱包充值总金额
     */
    @ExcelProperty(value = "当月钱包充值总金额")
    private BigDecimal rechargeTotalAmount;
    /**
     * 当月钱包消费总金额
     */
    @ExcelProperty(value = "当月钱包消费总金额")
    private BigDecimal expenditureTotalAmount;
    /**
     * 月末钱包余额
     */
    @ExcelProperty(value = "月末钱包余额")
    private BigDecimal walletBalanceMonthEnd;

    /**
     * 币种符号
     */
    @ExcelIgnore
    private String currencySymbol;

}
