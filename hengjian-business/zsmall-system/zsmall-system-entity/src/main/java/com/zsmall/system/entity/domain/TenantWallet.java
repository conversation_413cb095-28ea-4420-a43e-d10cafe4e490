package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.payment.WalletAutoPayEnum;
import com.zsmall.common.enums.payment.WalletStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 租户钱包对象 tenant_wallet
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_wallet")
public class TenantWallet extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 钱包唯一编号
     */
    private String walletNo;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种符号
     */
    private String currencySymbol;

    /**
     * 钱包余额
     */
    private BigDecimal walletBalance;

    /**
     * 钱包冻结金额
     */
    private BigDecimal walletFreezeAmount;

    /**
     * 钱包乐观锁
     */
    @Version
    private Integer walletVersion;

    /**
     * 钱包状态：0-不可用（被完全冻结），1-可用
     */
    private WalletStateEnum walletState;

    /**
     * 自动扣款，0：不自动扣款 1：自动扣款
     */
    private WalletAutoPayEnum isAutoPay;
}
