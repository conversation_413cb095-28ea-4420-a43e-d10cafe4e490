package com.zsmall.system.entity.domain;

import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.hengjian.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * payonner刷新token记录对象 payonner_refresh_token_record
 *
 * <AUTHOR> Li
 * @date 2024-10-10
 */
@Data
@Accessors(chain = true)
@TableName("payonner_refresh_token_record")
public class PayonnerRefreshTokenRecord {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 店铺名称
     */
    private String accountId;

    private String tenantId;

    /**
     * token类型
     */
    private String tokenType;

    /**
     * access_token
     */
    private String accessToken;

    /**
     * scope
     */
    private String scope;

    /**
     * 初始同意时间的 Unix 时间戳
     */
    private Long consentedOn;

    /**
     * 访问令牌有效的时间段（以秒为单位）
     */
    private Long expiresIn;

    /**
     * refresh_token
     */
    private String refreshToken;

    /**
     * 刷新令牌有效的时间段（以秒为单位）
     */
    private Long refreshTokenExpiresIn;

    /**
     * 是否刷新过 1：未刷新 2：已刷新
     */
    private Integer isRefresh;


}
