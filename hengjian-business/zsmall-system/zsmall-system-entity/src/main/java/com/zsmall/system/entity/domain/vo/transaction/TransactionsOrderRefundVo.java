package com.zsmall.system.entity.domain.vo.transaction;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.system.entity.domain.TransactionsOrderRefund;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 交易记录-售后主单关联视图对象 transactions_orders
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TransactionsOrderRefund.class)
public class TransactionsOrderRefundVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 交易记录主键
     */
    @ExcelProperty(value = "交易记录主键")
    private Long transactionsId;

    /**
     * 售后主单主键
     */
    @ExcelProperty(value = "订单主键")
    private Long orderRefundId;


}
