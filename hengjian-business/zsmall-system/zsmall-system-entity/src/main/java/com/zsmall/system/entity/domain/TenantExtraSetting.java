package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 租户额外设置对象 tenant_extra_setting
 *
 * <AUTHOR>
 * @date 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_extra_setting")
public class TenantExtraSetting extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设置编码
     */
    private String settingCode;

    /**
     * 设置内容
     */
    private String settingValue;

    /**
     * 分类状态
     */
    private Integer state;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
