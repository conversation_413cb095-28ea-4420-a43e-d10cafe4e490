package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.Bill;
import com.zsmall.system.entity.domain.BillRelation;
import com.zsmall.system.entity.domain.vo.bill.BillVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface Bill<PERSON><PERSON><PERSON> extends BaseMapperPlus<Bill, BillVo> {

    List<Bill> queryLacksAbstract(@Param("billNo") String billNo);

    Page<Bill> queryPage(@Param("queryType") String queryType, @Param("queryValue") String queryValue,
                         @Param("begin") LocalDateTime begin, @Param("end") LocalDateTime end, Page<Bill> queryPage);

    List<Bill> queryPage(@Param("queryType") String queryType, @Param("queryValue") String queryValue,
                         @Param("begin") LocalDateTime begin, @Param("end") LocalDateTime end);

    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumCircularDeposit(@Param("tenantId") String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumCurrentTotalAmountByBillNoList(@Param("billNoList") List<String> billNoList);

    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumUnsettledTotalAmount(@Param("tenantId") String tenantId);


    List<BillRelation> getAllBillRelation(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<String> getAllBillRelationDetailsByBillIdSet(@Param("billIdSet") Set<Long> billIdSet);

    List<Bill> getBillRelationDetailByBillIDSet(@Param("billIdSet") Set<Long> billIdSet);

    @Select("select  *from bill where bill_no= #{billNo}")
    Bill selectBill(String billNo);
}




