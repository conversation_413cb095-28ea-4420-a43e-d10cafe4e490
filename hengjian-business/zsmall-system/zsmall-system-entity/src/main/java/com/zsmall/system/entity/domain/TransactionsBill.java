package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 交易记录-账单关联对象 transactions_orders
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Data
@TableName("transactions_bill")
public class TransactionsBill implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 交易记录主键
     */
    private Long transactionsId;

    /**
     * 账单主键
     */
    private Long billId;

    /**
     * 创建时间
     */
    private Date createTime;


}
