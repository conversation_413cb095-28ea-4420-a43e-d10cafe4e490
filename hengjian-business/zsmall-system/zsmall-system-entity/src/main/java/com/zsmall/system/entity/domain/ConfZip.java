package com.zsmall.system.entity.domain;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/12 16:26
 */

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 邮编表
 * @TableName conf_zip
 */
@Data
@TableName(value = "conf_zip", autoResultMap = true)
public class ConfZip implements Serializable {

    /**
     * 主键
     */
    @NotNull(message="[主键]不能为空")
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 国家
     */
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("国家")
    @Length(max= 20,message="编码长度不能超过20")
    private String country;
    /**
     * 州代码
     */
    @NotBlank(message="[州代码]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("州代码")
    @Length(max= 10,message="编码长度不能超过10")
    private String stateCode;
    /**
     * 州名称
     */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("州名称")
    @Length(max= 64,message="编码长度不能超过64")
    private String stateName;
    /**
     * 州府
     */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("州府")
    @Length(max= 64,message="编码长度不能超过64")
    private String primaryCity;
    /**
     * 邮编
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("邮编")
    @Length(max= 10,message="编码长度不能超过10")
    private String zip;
    /**
     * 类型
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("类型")
    @Length(max= 10,message="编码长度不能超过10")
    private String zipType;
    /**
     * acceptable_cities
     */
    @Size(max= 120,message="编码长度不能超过120")
    @ApiModelProperty("acceptable_cities")
    @Length(max= 120,message="编码长度不能超过120")
    private String acceptableCities;
    /**
     * unacceptable_cities
     */
    @Size(max= 120,message="编码长度不能超过120")
    @ApiModelProperty("unacceptable_cities")
    @Length(max= 120,message="编码长度不能超过120")
    private String unacceptableCities;
    /**
     * 县/郡
     */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("县/郡")
    @Length(max= 64,message="编码长度不能超过64")
    private String county;
    /**
     * 邮编到
     */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("邮编到")
    @Length(max= 64,message="编码长度不能超过64")
    private String timezone;
    /**
     * 地区代码
     */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("地区代码")
    @Length(max= 64,message="编码长度不能超过64")
    private String areaCodes;
    /**
     * 维度
     */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("维度")
    @Length(max= 64,message="编码长度不能超过64")
    private String latitude;
    /**
     * 经度
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("经度")
    @Length(max= 10,message="编码长度不能超过10")
    private String longitude;
    /**
     * 地区代码
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("地区代码")
    @Length(max= 10,message="编码长度不能超过10")
    private String worldRegion;
    /**
     * 废弃
     */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("废弃")
    @Length(max= 64,message="编码长度不能超过64")
    private String decommissioned;
    /**
     * 估计人口
     */
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("估计人口")
    @Length(max= 10,message="编码长度不能超过10")
    private String estimatedPopulation;
    /**
     * 备注
     */
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("备注")
    @Length(max= 200,message="编码长度不能超过200")
    private String notes;
    /**
     *
     */
    @ApiModelProperty("")
    private Date lastUpdatedStamp;
    /**
     *
     */
    @ApiModelProperty("")
    private Date createdStamp;



}
