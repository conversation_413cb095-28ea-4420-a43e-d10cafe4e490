package com.zsmall.system.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import lombok.Data;

import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/22 16:04
 */
@Data
@TableName(value ="order_item_tracking_import_record")
public class OrderItemTrackingImportRecordSystem {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 导入单号
     */
    private String importRecordNo;

    /**
     * 导入名称
     */
    private String importFileName;

    /**
     * 导入tracking数量
     */
    private Integer importTrackingNum;


    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject importMessage;

    /**
     * 导入状态: Failed,Cancel,Pending,Importing,Success
     */
    private ImportStateEnum importState;

    /**
     * 删除标志(0代表存在 2代表删除)
     */
    private String delFlag;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
