package com.zsmall.system.entity.domain.vo.downloadRecord;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应信息-下载记录
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "响应信息-下载记录")
public class RespDownloadRecordBody {

  @Schema(title = "key")
  private Long key;

  @Schema(title = "时间")
  private String date;

  @Schema(title = "文件名")
  private String fileName;

  @Schema(title = "文件大小")
  private String fileSize;

  @Schema(title = "下载URl")
  private String downloadUrl;

  @Schema(title = "记录状态")
  private String recordStatus;

}
