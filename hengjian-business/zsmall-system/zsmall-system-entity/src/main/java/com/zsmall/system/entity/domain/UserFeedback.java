package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.userFeedback.ReplyStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户反馈表
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "user_feedback")
public class UserFeedback extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 租户类型：Distributor-分销商，Supplier-供应商
     */
    @TableField(value = "tenant_type")
    private String tenantType;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 反馈标题
     */
    @TableField(value = "feedback_title")
    private String feedbackTitle;

    /**
     * 反馈类型
     */
    @TableField(value = "feedback_type")
    private String feedbackType;

    /**
     * 反馈内容
     */
    @TableField(value = "feedback_content")
    private String feedbackContent;

    /**
     * 答复内容
     */
    @TableField(value = "reply_content")
    private String replyContent;

    /**
     * 答复状态：NoReply-未回复，Replied-已回复
     */
    @TableField(value = "reply_state")
    private ReplyStateEnum replyState = ReplyStateEnum.NoReply;

    /**
     * 答复时间
     */
    @TableField(value = "reply_time")
    private Date replyTime;

    /**
     * 答复人租户编号
     */
    @TableField(value = "replier_id")
    private String replierId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
