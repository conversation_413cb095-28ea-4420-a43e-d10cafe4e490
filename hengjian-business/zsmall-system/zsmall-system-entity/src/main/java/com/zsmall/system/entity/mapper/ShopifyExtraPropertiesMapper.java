package com.zsmall.system.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.system.entity.domain.ShopifyExtraProperties;
import com.zsmall.system.entity.domain.vo.salesChannel.ShopifyExtraPropertiesVo;
import org.apache.ibatis.annotations.Param;

/**
 * Shopify额外属性Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
public interface ShopifyExtraPropertiesMapper extends BaseMapperPlus<ShopifyExtraProperties, ShopifyExtraPropertiesVo> {

    /**
     * 根据店铺名查询扩展信息
     * @param shopDomain
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    ShopifyExtraPropertiesVo queryByDomain(@Param("channelName") String shopDomain);
}
