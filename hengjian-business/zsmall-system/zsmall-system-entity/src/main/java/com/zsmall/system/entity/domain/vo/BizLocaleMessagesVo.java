package com.zsmall.system.entity.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.system.entity.domain.BizLocaleMessages;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;



/**
 * 翻译消息数据视图对象 biz_locale_messages
 *
 * <AUTHOR> Li
 * @date 2024-08-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizLocaleMessages.class)
public class BizLocaleMessagesVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String id;

    /**
     * 所属子系统id
     */
    @ExcelProperty(value = "所属子系统id")
    private Long systemId;

    /**
     * 所属语言包标识
     */
    @ExcelProperty(value = "所属语言包标识")
    private String locale;

    /**
     * 所属标识生成的lange_key,格式:[site].[package].[model].[group].key
     */
    @ExcelProperty(value = "所属标识生成的lange_key,格式:[site].[package].[model].[group].key")
    private String langKey;

    /**
     * 所属语言翻译值数据
     */
    @ExcelProperty(value = "所属语言翻译值数据")
    private String langValue;

    /**
     * 操作时间
     */
//    @ExcelProperty(value = "操作时间")
//    private Date opAt;

    /**
     * 标识描述
     */
    private String langKeyDesc;


    private Date createTime;
}
