package com.zsmall.system.entity.domain.dto.settleInBasic;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-供应商入驻公司联系人信息
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserSupSettleInContactChangeBody {

    /**
     * 姓
     */
    private String firstNameBefore;
    private String firstNameAfter;
    /**
     * 名
     */
    private String lastNameBefore;
    private String lastNameAfter;
    /**
     * 姓名
     */
    private String nameBefore;
    private String nameAfter;
    /**
     * 手机号码国际区号
     */
    private String areaCodeBefore;
    private String areaCodeAfter;
    /**
     * 手机号码
     */
    private String phoneNumberBefore;
    private String phoneNumberAfter;
    /**
     * 邮箱
     */
    private String emailBefore;
    private String emailAfter;
    /**
     * 即时通讯账号
     */
    private String msgAppAccountBefore;
    private String msgAppAccountAfter;
    /**
     * 即时通讯类型
     */
    private String msgAppTypeBefore;
    private String msgAppTypeAfter;
    /**
     * 联系人类型
     */
    private String contactTypeBefore;
    private String contactTypeAfter;
    /**
     * 国家Id
     */
    private Long countryIdBefore;
    private Long countryIdAfter;
    /**
     * 省/州Id
     */
    private Long stateIdBefore;
    private Long stateIdAfter;
    /**
     * 省/州 文本
     */
    private String stateTextBefore;
    private String stateTextAfter;
    /**
     * 城市 文本"
     */
    private String cityTextBefore;
    private String cityTextAfter;
    /**
     * 具体地址
     */
    private String addressBefore;
    private String addressAfter;

}
