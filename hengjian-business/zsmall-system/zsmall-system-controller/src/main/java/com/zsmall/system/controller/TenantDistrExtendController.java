package com.zsmall.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.system.biz.service.ITenantDistrExtendService;
import com.zsmall.system.entity.domain.bo.settleInBasic.TenantDistrExtendBo;
import com.zsmall.system.entity.domain.vo.settleInBasic.TenantDistrExtendVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 租户分销商-拓展信息
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/distrExtend")
public class TenantDistrExtendController extends BaseController {

    private final ITenantDistrExtendService tenantDistrExtendService;

    /**
     * 查询租户分销商-拓展信息列表
     */
    @SaCheckPermission("business:distrExtend:list")
    @GetMapping("/list")
    public TableDataInfo<TenantDistrExtendVo> list(TenantDistrExtendBo bo, PageQuery pageQuery) {
        return tenantDistrExtendService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出租户分销商-拓展信息列表
     */
    @SaCheckPermission("business:distrExtend:export")
    @Log(title = "租户分销商-拓展信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TenantDistrExtendBo bo, HttpServletResponse response) {
        List<TenantDistrExtendVo> list = tenantDistrExtendService.queryList(bo);
        ExcelUtil.exportExcel(list, "租户分销商-拓展信息", TenantDistrExtendVo.class, response, false);
    }

    /**
     * 获取租户分销商-拓展信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:distrExtend:query")
    @GetMapping("/{id}")
    public R<TenantDistrExtendVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(tenantDistrExtendService.queryById(id));
    }

    /**
     * 保存分销商注册信息，新增和修改使用同一个接口
     */
    @Log(title = "保存分销商注册信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TenantDistrExtendBo bo) throws Exception {
        return toAjax(tenantDistrExtendService.insertByBo(bo));
    }

    /**
     * 修改租户分销商-拓展信息
     */
    @Log(title = "租户分销商-拓展信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TenantDistrExtendBo bo) throws Exception {
        return toAjax(tenantDistrExtendService.updateByBo(bo));
    }

    /**
     * 删除租户分销商-拓展信息
     *
     * @param ids 主键串
     */
    @Log(title = "租户分销商-拓展信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(tenantDistrExtendService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 判断是否提示分销商填写拓展信息
     *
     */
    @GetMapping("/hintPerfectExtendedInformation")
    public R<Boolean> hintPerfectExtendedInformation() {
        return R.ok(tenantDistrExtendService.hintPerfectExtendedInformation());
    }

    /**
     *获取分销商入驻详情
     */
    @GetMapping("getInfo")
    public R<TenantDistrExtendVo> getInfo(String tenantId) {
        return R.ok(tenantDistrExtendService.getInfo(tenantId));
    }


}
