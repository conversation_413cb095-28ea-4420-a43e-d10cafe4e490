package com.zsmall.system.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.SimpleSysUserEvent;
import com.hengjian.extend.utils.SystemEventUtils;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.system.biz.service.ShippingReturnsService;
import com.zsmall.system.entity.domain.ShippingReturns;
import com.zsmall.system.entity.domain.bo.ShippingReturnsBo;
import com.zsmall.system.entity.domain.vo.ShippingReturnsVo;
import com.zsmall.system.entity.iservice.IShippingReturnsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 物流与退货模板Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-06-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ShippingReturnsServiceImpl implements ShippingReturnsService {

    private final IShippingReturnsService iShippingReturnsService;
    private static final Pattern PATTERN = Pattern.compile("<.+?>", Pattern.DOTALL);


    /**
     * 查询物流与退货模板
     */
    @Override
    public ShippingReturnsVo queryById(Long id) {
        return iShippingReturnsService.queryById(id);
    }

    /**
     * 查询物流与退货模板列表
     */
    @Override
    public TableDataInfo<ShippingReturnsVo> supPage(ShippingReturnsBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        String tenantId = loginUser.getTenantId();
        List<ShippingReturnsVo> result = new ArrayList<>();
        int zsmallShippingReturnsDefaultState = 1;
        ShippingReturnsVo zsmallShippingReturns = iShippingReturnsService.queryById(0L);
        result.add(zsmallShippingReturns);
        LambdaQueryWrapper<ShippingReturns> lqw = buildQueryWrapper(bo);
        lqw.eq(ShippingReturns::getTenantId, tenantId).eq(ShippingReturns::getDelFlag, 0)
            .orderByDesc(ShippingReturns::getCreateTime);
        List<ShippingReturnsVo> shippingReturnsVos = iShippingReturnsService.selectVoList(lqw);
        if (CollUtil.isNotEmpty(shippingReturnsVos)) {
            for (ShippingReturnsVo shippingReturnsVo : shippingReturnsVos) {
                Integer defaultState = shippingReturnsVo.getDefaultState();
                if (ObjectUtil.equals(defaultState, 1)) {
                    zsmallShippingReturnsDefaultState = 0;
                }
            }
        }
        zsmallShippingReturns.setDefaultState(zsmallShippingReturnsDefaultState);
        result.addAll(shippingReturnsVos);
        return TableDataInfo.build(result);
    }

    /**
     * 查询物流与退货模板列表
     */
    @Override
    public List<ShippingReturnsVo> queryList(ShippingReturnsBo bo) {
        LambdaQueryWrapper<ShippingReturns> lqw = buildQueryWrapper(bo);
        return iShippingReturnsService.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ShippingReturns> buildQueryWrapper(ShippingReturnsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ShippingReturns> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), ShippingReturns::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getSubmitReason()), ShippingReturns::getSubmitReason, bo.getSubmitReason());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ShippingReturns::getContent, bo.getContent());
        lqw.eq(bo.getStatus() != null, ShippingReturns::getStatus, bo.getStatus());
        lqw.eq(bo.getDefaultState() != null, ShippingReturns::getDefaultState, bo.getDefaultState());
        lqw.eq(StringUtils.isNotBlank(bo.getRejectReason()), ShippingReturns::getRejectReason, bo.getRejectReason());
        lqw.eq(bo.getReviewTime() != null, ShippingReturns::getReviewTime, bo.getReviewTime());
        return lqw;
    }

    /**
     * 新增物流与退货模板
     */
    @Override
    public Boolean insertByBo(ShippingReturnsBo bo) throws Exception {
        LoginUser loginUser = LoginHelper.getLoginUser();
        String tenantId = loginUser.getTenantId();
        Long userId = loginUser.getUserId();
        ShippingReturns add = MapstructUtils.convert(bo, ShippingReturns.class);
        validEntityBeforeSave(add);
        //
        add.setName("Customize Shipping & Return");
        add.setDefaultState(0);
        add.setDelFlag("0");
        add.setStatus(1L);
        add.setTenantId(tenantId);
        add.setUserId(userId);

        boolean flag = iShippingReturnsService.save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改物流与退货模板
     */
    @Override
    public Boolean updateByBo(ShippingReturnsBo bo) throws Exception {
        ShippingReturns update = MapstructUtils.convert(bo, ShippingReturns.class);
        validEntityBeforeSave(update);

        update.setStatus(1L);
        update.setDefaultState(0);

        return iShippingReturnsService.updateById(update);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ShippingReturns entity) throws Exception {
        // 做一些数据校验,如唯一约束
        String content = entity.getContent();
        Matcher matcher = PATTERN.matcher(content);
        String str = matcher.replaceAll("");
        System.out.println(str);

        if (StrUtil.isBlank(str)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SHIPPING_RETURNS_CONTENT_IS_BLANK.args(str));
        }
    }

    /**
     * 批量删除物流与退货模板
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return iShippingReturnsService.removeBatchByIds(ids);
    }

    @Override
    public R<Void> updateDefaultShippingReturns(ShippingReturnsBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        String tenantId = loginUser.getTenantId();

        Long boId = bo.getId();

        LambdaQueryWrapper<ShippingReturns> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ShippingReturns::getTenantId, tenantId).eq(ShippingReturns::getDelFlag, "0")
            .eq(ShippingReturns::getStatus, 2L).eq(ShippingReturns::getDefaultState, 1);
        if (ObjectUtil.isNotNull(boId)) {
            lqw.ne(ShippingReturns::getId, boId);
        }

        List<ShippingReturns> shippingReturns = iShippingReturnsService.list(lqw);
        if (CollUtil.isNotEmpty(shippingReturns)) {
            for (ShippingReturns shippingReturn : shippingReturns) {
                shippingReturn.setDefaultState(0);
            }
            iShippingReturnsService.updateBatchById(shippingReturns);
        }

        if (!ObjectUtil.equals(boId, 0L)) {
            ShippingReturns shippingReturns1 = iShippingReturnsService.getById(boId);
            shippingReturns1.setDefaultState(1);
            iShippingReturnsService.updateById(shippingReturns1);
        }

        return R.ok();
    }

    @Override
    public TableDataInfo<ShippingReturns> staffPage(ShippingReturnsBo bo, PageQuery pageQuery) {
        String content = bo.getContent();
        Long status = bo.getStatus();
        String queryTime = bo.getQueryTime();
        LambdaQueryWrapper<ShippingReturns> lqw = new LambdaQueryWrapper<>();
        lqw.ne(ShippingReturns::getId, 0L);
        lqw.like(StrUtil.isNotBlank(content), ShippingReturns::getContent, content)
            .eq(ObjectUtil.isNotNull(status), ShippingReturns::getStatus, status);
        if (StrUtil.isNotBlank(queryTime)) {
            lqw.ge(ShippingReturns::getCreateTime, queryTime).le(ShippingReturns::getCreateTime, queryTime + " 23:59:59");
        }
        lqw.orderByDesc(ShippingReturns::getCreateTime);
        Page<ShippingReturns> page = iShippingReturnsService.page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public R<Void> reviewShippingReturn(ShippingReturnsBo bo) {
        Long id = bo.getId();
        Long status = bo.getStatus();
        ShippingReturns shippingReturns = iShippingReturnsService.getById(id);

        shippingReturns.setStatus(status);
        shippingReturns.setRejectReason(null);
        if (status == 3) {
            shippingReturns.setRejectReason(bo.getRejectReason());
            shippingReturns.setDefaultState(0);
        }
        TenantHelper.ignore(() -> iShippingReturnsService.updateById(shippingReturns));
        return R.ok();
    }

    @Override
    public ShippingReturnsVo getDetailShippingReturnsMd(ShippingReturnsBo bo) {
        Long id = bo.getId();
        ShippingReturnsVo vo = iShippingReturnsService.queryById(id);
        String tenantId = vo.getTenantId();
        vo.setRejectReason(null);
        SimpleSysUserEvent.SimpleUser sysUser = SystemEventUtils.getSimpleSysUser(vo.getUserId());
        vo.setUserName(sysUser.getUserName());
        vo.setTenantId(tenantId);
        vo.setEmail(sysUser.getEmail());
        vo.setPhonenumber(sysUser.getPhoneNumber());
        return vo;
    }

}
