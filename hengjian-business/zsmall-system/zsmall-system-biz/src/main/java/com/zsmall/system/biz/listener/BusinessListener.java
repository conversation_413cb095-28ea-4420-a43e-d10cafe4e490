package com.zsmall.system.biz.listener;

import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.event.system.CheckDisInfoPerfectionEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 业务相关事件监听器
 *
 * <AUTHOR>
 * @date 2023/9/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessListener {

    private final ISysTenantService tenantService;

    @EventListener
    @InMethodLog("检查分销商是否完善信息事件监听")
    public void initExtraSettingEventListener(CheckDisInfoPerfectionEvent event) {
        SysTenantVo sysTenantVo = TenantHelper.ignore(() -> tenantService.queryByTenantId(event.getTenantId()));
        String extraPerfectionFlag = sysTenantVo.getExtraPerfectionFlag();
        if (!StrUtil.equals(extraPerfectionFlag, "1")) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.USER_BULK_INFORMATION_IS_NOT_PERFECT);
        }
    }

}
