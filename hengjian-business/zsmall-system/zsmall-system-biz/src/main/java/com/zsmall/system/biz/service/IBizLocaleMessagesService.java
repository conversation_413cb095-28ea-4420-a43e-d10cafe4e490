package com.zsmall.system.biz.service;

import com.alibaba.fastjson.JSONObject;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.system.entity.domain.BizLocaleMessages;
import com.zsmall.system.entity.domain.bo.BizLocaleMessagesBo;
import com.zsmall.system.entity.domain.vo.BizLocaleMessagesVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 翻译消息数据Service接口
 *
 * <AUTHOR> Li
 * @date 2024-08-06
 */
public interface IBizLocaleMessagesService {

    /**
     * 查询翻译消息数据
     */
    BizLocaleMessagesVo queryById(String id);


    List<String> getHeader();

    /**
     * 查询翻译消息数据列表
     */
    TableDataInfo<BizLocaleMessagesVo> queryPageList(BizLocaleMessagesBo bo, PageQuery pageQuery);

    /**
     * 动态查询翻译消息数据列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<List<Map<String, Object>>> dynamicPage(BizLocaleMessagesBo bo, PageQuery pageQuery);

    /**
     * 获取全部的翻译消息数据
     *
     * @param locale
     * @return
     */
    List<BizLocaleMessages> getAllData(String locale);

    /**
     * 获取全部的翻译消息数据 json类型
     *
     * @param locale
     * @return
     */
    JSONObject getAllJsonData(String locale);

    /**
     * 获取指定语种的翻译消息数据 json string 类型
     *
     * @param locale
     * @return
     */
    String getAllJsonStringData(String locale);

    /**
     * 查询翻译消息数据列表
     */
    List<BizLocaleMessagesVo> queryList(BizLocaleMessagesBo bo);

    /**
     * 新增翻译消息数据
     */
    Boolean insertByBo(BizLocaleMessagesBo bo);

    /**
     * 新增
     *
     * @param bizLocaleMessages
     */
    void add(Map<String, Object> bizLocaleMessages);

    /**
     * 修改翻译消息数据
     */
    Boolean updateByBo(BizLocaleMessagesBo bo);

    /**
     * 编辑
     *
     * @param bizLocaleMessages
     */
    void update(Map<String, Object> bizLocaleMessages);

    /**
     * 校验并批量删除翻译消息数据信息
     */
    Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid);

    /**
     * 删除
     *
     * @param langKey
     */
    void delete(String langKey);

    /**
     * 获取excel模板表头
     *
     * @return
     */
    List<String> getExportTemplateTableHeader();

    /**
     * 获取excel导出表头
     *
     * @return
     */
    List<String> getExportTableHeader();

    /**
     * 根据条件查询导出数据
     *
     * @param bo
     * @return
     */
    List<Map<String, Object>> getExcelData(BizLocaleMessagesBo bo);
}
