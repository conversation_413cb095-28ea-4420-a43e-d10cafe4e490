package com.zsmall.system.biz.service.impl;

import cn.hutool.json.JSONArray;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.extend.utils.ZSMallActivityEventUtils;
import com.zsmall.system.biz.service.BusinessNoticeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 业务消息通知-实现
 *
 * <AUTHOR>
 * @date 2023/8/5
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessNoticeServiceImpl implements BusinessNoticeService {

    /**
     * 发送业务通知（通用方法，若有其他需求，可以再单独写接口）
     *
     * @return
     */
    @Override
    public R<JSONArray> sendBusinessNotice() {
        log.info("Socket连接成功，开始准备发送业务通知");

        LoginUser loginUser = LoginHelper.getLoginUser();
        String tenantId = loginUser.getTenantId();
        TenantType tenantType = LoginHelper.getTenantTypeEnum();

        JSONArray array = null;
        switch (tenantType) {
            // 分销商相关消息通知
            case Distributor:
                log.info("发送业务通知 => 当前用户：分销商{}", tenantId);
                // 发送活动即将到期通知
                array = ZSMallActivityEventUtils.activityExpirationNotice();
                break;
            default:
                break;
        }
        return R.ok(array);
    }
}
