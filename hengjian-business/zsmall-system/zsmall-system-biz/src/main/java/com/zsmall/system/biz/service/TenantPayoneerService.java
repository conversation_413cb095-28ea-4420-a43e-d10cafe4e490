package com.zsmall.system.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.ijpay.payoneer.model.out.PaymentCommit;
import com.ijpay.payoneer.model.out.RegistrationLinkModel;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.payment.PayoneerCommitBo;
import com.zsmall.system.entity.domain.bo.payment.PayoneerPaymentBo;
import com.zsmall.system.entity.domain.vo.extraSetting.PayoneerPaymentVo;
import com.zsmall.system.entity.domain.vo.payment.PayoneerCommitVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * tenant payoneer Service
 *
 * <AUTHOR> @create 2023年6月17日
 */
public interface TenantPayoneerService {

    /**
     * 支付下单
     * @param bo
     * @return
     */
    R<PayoneerPaymentVo> getPaymentDebitInfo(PayoneerPaymentBo bo);

    /**
     * Payoneer重定向
     * @param request
     * @param response
     */
    void payoneerRedirect(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 获取注册地址
     * @return
     */
    R<RegistrationLinkModel> getRegistrationLink();

    /**
     * 取消支付下单
     * @param orderNo
     * @return
     */
    boolean cancelPaymentDebit(String orderNo) throws RStatusCodeException;

    /**
     * 支付确认
     * @param payoneerCommitBo
     * @return
     */
    PayoneerCommitVo toPaymentCommit(PayoneerCommitBo payoneerCommitBo) throws RStatusCodeException;

    /**
     * 二次确认支付成功后 处理钱包余额相关操作
     *
     * @param transactionRecord
     * @param accountId         账号Id
     * @param paymentCommit
     */
    void reconfirmRechargeAndRecordTransaction(TransactionRecord transactionRecord, String accountId, PaymentCommit paymentCommit) throws Exception;

    /**
     * 二次确认支付成功后 处理钱包余额相关操作 审核版本
     *
     * @param transactionRecord
     * @param accountId
     * @param paymentCommit
     * @throws Exception
     */
    void reconfirmRechargeAndRecordTransactionCheck(TransactionRecord transactionRecord, String accountId, PaymentCommit paymentCommit) throws Exception;


}
