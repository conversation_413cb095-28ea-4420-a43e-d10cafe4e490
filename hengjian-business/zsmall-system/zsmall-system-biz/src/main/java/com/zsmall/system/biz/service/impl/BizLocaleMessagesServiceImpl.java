package com.zsmall.system.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysDictDataVo;
import com.hengjian.system.service.ISysDictDataService;
import com.hengjian.system.service.ISysDictTypeService;
import com.hengjian.system.service.impl.SysDictDataServiceImpl;
import com.zsmall.system.biz.service.IBizLocaleMessagesService;
import com.zsmall.system.entity.domain.BizLocaleMessages;
import com.zsmall.system.entity.domain.bo.BizLocaleMessagesBo;
import com.zsmall.system.entity.domain.vo.BizLocaleMessagesVo;
import com.zsmall.system.entity.mapper.BizLocaleMessagesMapper;
import lombok.RequiredArgsConstructor;
import net.sf.jsqlparser.statement.select.Wait;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 翻译消息数据Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2024-08-06
 */
@RequiredArgsConstructor
@Service
public class BizLocaleMessagesServiceImpl implements IBizLocaleMessagesService {

    private final BizLocaleMessagesMapper baseMapper;

    private final ISysDictTypeService iSysDictTypeService;

    /**
     * 查询翻译消息数据
     */
    @Override
    public BizLocaleMessagesVo queryById(String id) {
        return TenantHelper.ignore(() -> baseMapper.selectVoByIdNotTenant(id));
    }

    @Override
    public List<String> getHeader() {
        List<SysDictDataVo> dictData = iSysDictTypeService.selectDictDataByType("system_language_type");
        if (dictData.isEmpty()) {
            throw new RuntimeException("未找到字典数据");
        }
        List<String> headerList = new ArrayList<>();
        headerList.add("langKey");
        headerList.add("langKeyDesc");
        List<String> dictLabelList = dictData.stream().map(SysDictDataVo::getDictValue).collect(Collectors.toList());
        headerList.addAll(dictLabelList);
        headerList.add("createTime");
        return headerList;
    }

    /**
     * 查询翻译消息数据列表
     */
    @Override
    public TableDataInfo<BizLocaleMessagesVo> queryPageList(BizLocaleMessagesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizLocaleMessages> lqw = buildQueryWrapper(bo);
        Page<BizLocaleMessagesVo> result = TenantHelper.ignore(() -> baseMapper.selectVoPageNotTenant(pageQuery.build(), lqw));
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<List<Map<String, Object>>> dynamicPage(BizLocaleMessagesBo bo, PageQuery pageQuery) {
        // 获取表头数据
        List<SysDictDataVo> dictData = iSysDictTypeService.selectDictDataByType("system_language_type");
        if (dictData.isEmpty()) {
            throw new RuntimeException("未找到字典数据");
        }
        List<String> headerList = new ArrayList<>();
        headerList.add("langKey");
        headerList.add("langKeyDesc");
        List<String> dictLabelList = dictData.stream().map(SysDictDataVo::getDictValue).collect(Collectors.toList());
        headerList.addAll(dictLabelList);
        headerList.add("createTime");
        // 处理动态表头数据
        if (CollUtil.isNotEmpty(dictLabelList)) {
            List<Map<String, Object>> dataList = new ArrayList<>();
            LambdaQueryWrapper<BizLocaleMessages> lqw = buildQueryWrapper(bo);
            Page<BizLocaleMessagesVo> result = TenantHelper.ignore(() -> baseMapper.selectVoPageNotTenant(pageQuery.build(), lqw));
            LambdaQueryWrapper<BizLocaleMessages> lambdaQueryWrapper = buildQueryWrapperGroup(bo);
            Page<BizLocaleMessagesVo> resultTotal = TenantHelper.ignore(() -> baseMapper.selectVoPageNotTenant(pageQuery.build(), lambdaQueryWrapper));
            Integer i = 0;
            for (String header : dictLabelList) {
                Page<BizLocaleMessagesVo> resultLocal = result;
                // 不根据内容搜索，使用语言标识查询分页数据
                if (StringUtils.isEmpty(bo.getLangValue())) {
                    LambdaQueryWrapper<BizLocaleMessages> lqwFor = buildQueryWrapper(bo);
                    lqwFor.eq(BizLocaleMessages::getLocale, header);
                    resultLocal = TenantHelper.ignore(() -> baseMapper.selectVoPageNotTenant(pageQuery.build(), lqwFor));
                }
                if (null != resultLocal && CollUtil.isEmpty(resultLocal.getRecords())) {
                    for (Map<String, Object> map : dataList) {
                        if (!map.containsKey(header)) {
                            map.put(header, null);
                            break;
                        }
                    }
                }
                for (BizLocaleMessagesVo bizLocaleMessagesVo : resultLocal.getRecords()) {
                    // 第一次，固定数量
                    if (i == 0) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("langKey", bizLocaleMessagesVo.getLangKey());
                        map.put("langKeyDesc", bizLocaleMessagesVo.getLangKeyDesc());
                        map.put(bizLocaleMessagesVo.getLocale(), bizLocaleMessagesVo.getLangValue());
                        map.put("createTime", bizLocaleMessagesVo.getCreateTime());
                        if (!map.containsKey(header)) {
                            map.put(header, null);
                        }
                        dataList.add(map);
                    } else {
                        // 根据语种填充数据
                        for (Map<String, Object> map : dataList) {
                            if (!map.containsKey(header)) {
                                map.put(header, null);
                            }
                            if (bizLocaleMessagesVo.getLangKey().equals(map.get("langKey"))) {
                                map.put(bizLocaleMessagesVo.getLocale(), bizLocaleMessagesVo.getLangValue());
                                break;
                            }
                        }
                    }
                }
                i++;
            }
            // 补全没有填充的数据
            List<String> langKeyList = dataList.stream().map(map -> (String) map.get("langKey"))
                                               .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(langKeyList)) {
                LambdaQueryWrapper<BizLocaleMessages> lqwFor = Wrappers.lambdaQuery();
                lqwFor.in(BizLocaleMessages::getLangKey, langKeyList);
                lqwFor.in(BizLocaleMessages::getLocale, dictLabelList);
                List<BizLocaleMessages> bizLocaleMessagesList = TenantHelper.ignore(() -> baseMapper.selectList(lqwFor));
                for (Map<String, Object> map : dataList) {
                    for (String dictLabel : dictLabelList) {
                        for (BizLocaleMessages bizLocaleMessages : bizLocaleMessagesList) {
                            if (ObjectUtil.isNull(map.get(dictLabel)) && dictLabel.equals(bizLocaleMessages.getLocale()) && map.get("langKey")
                                                                                                                               .equals(bizLocaleMessages.getLangKey())) {
                                map.put(dictLabel, bizLocaleMessages.getLangValue());
                                break;
                            }
                        }
                    }
                }
            }
            return TableDataInfo.build(dataList, resultTotal.getTotal(), true);
        }
        return null;
    }


    public List<Map<String, Object>> getExcelData(BizLocaleMessagesBo bo) {
        // 获取表头数据
        List<SysDictDataVo> dictData = iSysDictTypeService.selectDictDataByType("system_language_type");
        if (dictData.isEmpty()) {
            throw new RuntimeException("未找到字典数据");
        }
        List<String> headerList = new ArrayList<>();
        List<String> dictLabelList = dictData.stream().map(SysDictDataVo::getDictLabel).collect(Collectors.toList());
        headerList.addAll(dictLabelList);
        // 处理动态表头数据
        if (CollUtil.isNotEmpty(headerList)) {
            List<Map<String, Object>> dataList = new ArrayList<>();
            LambdaQueryWrapper<BizLocaleMessages> lqw = buildQueryWrapper(bo);
            List<BizLocaleMessages> result = TenantHelper.ignore(() -> baseMapper.selectListNotTenant(lqw));
            Integer i = 0;
            for (String header : dictLabelList) {
                List<BizLocaleMessages> resultLocal = result;
                if (StringUtils.isEmpty(bo.getLangValue())) {
                    LambdaQueryWrapper<BizLocaleMessages> lqwFor = buildQueryWrapper(bo);
                    lqwFor.eq(BizLocaleMessages::getLocale, getDictDataByDictDataList(dictData, header, null));
                    resultLocal = TenantHelper.ignore(() -> baseMapper.selectListNotTenant(lqwFor));
                }
                if (CollUtil.isEmpty(resultLocal)) {
                    for (Map<String, Object> map : dataList) {
                        if (!map.containsKey(header)) {
                            map.put(header, null);
                            break;
                        }
                    }
                }
                for (BizLocaleMessages bizLocaleMessages : resultLocal) {
                    if (i == 0) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("标识", bizLocaleMessages.getLangKey());
                        map.put("标识描述", bizLocaleMessages.getLangKeyDesc());
                        map.put(getDictDataByDictDataList(dictData, null, bizLocaleMessages.getLocale()), bizLocaleMessages.getLangValue());
                        map.put("创建时间", bizLocaleMessages.getCreateTime().toInstant().atZone(ZoneId.systemDefault())
                                                             .toLocalDateTime());
                        if (!map.containsKey(header)) {
                            map.put(header, null);
                        }
                        dataList.add(map);
                    } else {
                        for (Map<String, Object> map : dataList) {
                            if (!map.containsKey(header)) {
                                map.put(header, null);
                            }
                            if (bizLocaleMessages.getLangKey().equals(map.get("标识"))) {
                                map.put(getDictDataByDictDataList(dictData, null, bizLocaleMessages.getLocale()), bizLocaleMessages.getLangValue());
                                break;
                            }
                        }
                    }
                }
                i++;
            }
            // 补全没有填充的数据
            List<String> langKeyList = dataList.stream().map(map -> (String) map.get("标识"))
                                               .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(langKeyList)) {
                List<String> localeList = new ArrayList<>();
                for (String dictLabel : dictLabelList) {
                    localeList.add(getDictDataByDictDataList(dictData, dictLabel, null));
                }
                if (CollUtil.isNotEmpty(localeList)) {
                    LambdaQueryWrapper<BizLocaleMessages> lqwFor = Wrappers.lambdaQuery();
                    lqwFor.in(BizLocaleMessages::getLangKey, langKeyList);
                    lqwFor.in(BizLocaleMessages::getLocale, localeList);
                    List<BizLocaleMessages> bizLocaleMessagesList = TenantHelper.ignore(() -> baseMapper.selectList(lqwFor));
                    for (Map<String, Object> map : dataList) {
                        for (String dictLabel : dictLabelList) {
                            for (BizLocaleMessages bizLocaleMessages : bizLocaleMessagesList) {
                                if (ObjectUtil.isNull(map.get(dictLabel)) && dictLabel.equals(getDictDataByDictDataList(dictData, null, bizLocaleMessages.getLocale())) && map.get("标识")
                                                                                                                                                                              .equals(bizLocaleMessages.getLangKey())) {
                                    map.put(dictLabel, bizLocaleMessages.getLangValue());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            return dataList;
        }
        return null;
    }

    @Override
    public List<BizLocaleMessages> getAllData(String locale) {
        if (StringUtils.isEmpty(locale)) {
            return null;
        }
        LambdaQueryWrapper<BizLocaleMessages> lqw = Wrappers.lambdaQuery();
        lqw.eq(BizLocaleMessages::getLocale, locale);
        return TenantHelper.ignore(() -> baseMapper.selectListNotTenant(lqw));
    }

    @Override
    public JSONObject getAllJsonData(String locale) {
        List<BizLocaleMessages> allData = getAllData(locale);
        if (CollUtil.isNotEmpty(allData)) {
            Map<String, Object> jsObject = new HashMap<>();
            for (BizLocaleMessages bizLocaleMessages : allData) {
                // 获取键和值
                String key = bizLocaleMessages.getLangKey();
                String value = bizLocaleMessages.getLangValue();
                // 将key分割成多个部分 使用-1作为limit以包含所有部分
                String[] parts = key.split("\\.", -1);
                Map<String, Object> currentMap = jsObject;
                // 遍历key的部分，构建嵌套的Map结构
                for (int i = 0; i < parts.length - 1; i++) {
                    // 如果当前Map中没有这个key，就添加一个新的Map
                    if (!currentMap.containsKey(parts[i])) {
                        currentMap.put(parts[i], new HashMap<>());
                    }
                    currentMap = (Map<String, Object>) currentMap.get(parts[i]);
                }
                // 设置最终的value
                currentMap.put(parts[parts.length - 1], value);
            }
            // 将最终的Map转换为JavaScript对象字面量格式的字符串
            String jsString = convertMapToJsString3(jsObject);
            if (StringUtils.isNotEmpty(jsString)) {
                JSONObject allDataJson = JSONObject.parseObject(jsString);
                return allDataJson;
            }
            return null;
        }
        return null;
    }

    @Override
    public String getAllJsonStringData(String locale) {
        List<BizLocaleMessages> allData = getAllData(locale);
        if (CollUtil.isNotEmpty(allData)) {
            Map<String, Object> jsObject = new HashMap<>();
            for (BizLocaleMessages bizLocaleMessages : allData) {
                // 获取键和值
                String key = bizLocaleMessages.getLangKey();
                String value = bizLocaleMessages.getLangValue();
                // 将key分割成多个部分 使用-1作为limit以包含所有部分
                String[] parts = key.split("\\.", -1);
                Map<String, Object> currentMap = jsObject;
                for (int i = 0; i < parts.length - 1; i++) {
                    String part = parts[i];
                    // 如果当前Map中没有这个key，添加一个新的Map
                    if (!currentMap.containsKey(part)) {
                        currentMap.put(part, new HashMap<>());
                    }
                    // 如果当前key已经存在并且不是Map，那么替换为一个Map并保留当前的值
                    if (!(currentMap.get(part) instanceof Map)) {
                        currentMap.put(part, new HashMap<>());
                    }
                    currentMap = (Map<String, Object>) currentMap.get(part);
                }
                // 设置最终的value
                currentMap.put(parts[parts.length - 1], value);
            }
            // 将最终的Map转换为JavaScript对象字面量格式的字符串
            String jsString = convertMapToJsString3(jsObject);
            return jsString;
        }
        return null;
    }

    /**
     * 查询翻译消息数据列表
     */
    @Override
    public List<BizLocaleMessagesVo> queryList(BizLocaleMessagesBo bo) {
        LambdaQueryWrapper<BizLocaleMessages> lqw = buildQueryWrapper(bo);
        return TenantHelper.ignore(() -> baseMapper.selectVoListNotTenant(lqw));
    }

    private LambdaQueryWrapper<BizLocaleMessages> buildQueryWrapper(BizLocaleMessagesBo bo) {
        LambdaQueryWrapper<BizLocaleMessages> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSystemId() != null, BizLocaleMessages::getSystemId, bo.getSystemId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocale()), BizLocaleMessages::getLocale, bo.getLocale());
        lqw.like(StringUtils.isNotBlank(bo.getLangKey()), BizLocaleMessages::getLangKey, bo.getLangKey());
        lqw.eq(StringUtils.isNotBlank(bo.getExactLangKey()), BizLocaleMessages::getLangKey, bo.getExactLangKey());
        lqw.like(StringUtils.isNotBlank(bo.getLangValue()), BizLocaleMessages::getLangValue, bo.getLangValue());
        lqw.like(StringUtils.isNotBlank(bo.getLangKeyDesc()), BizLocaleMessages::getLangKeyDesc, bo.getLangKeyDesc());
        lqw.ge(StringUtils.isNotBlank(bo.getStartDate()), BizLocaleMessages::getCreateTime, bo.getStartDate());
        lqw.le(StringUtils.isNotBlank(bo.getEndDate()), BizLocaleMessages::getCreateTime, bo.getEndDate());
        lqw.orderByDesc(BizLocaleMessages::getId);
        return lqw;
    }

    /**
     * 分组查询翻译消息数据列表
     *
     * @param bo
     * @return
     */
    private LambdaQueryWrapper<BizLocaleMessages> buildQueryWrapperGroup(BizLocaleMessagesBo bo) {
        LambdaQueryWrapper<BizLocaleMessages> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getSystemId() != null, BizLocaleMessages::getSystemId, bo.getSystemId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocale()), BizLocaleMessages::getLocale, bo.getLocale());
        lqw.like(StringUtils.isNotBlank(bo.getLangKey()), BizLocaleMessages::getLangKey, bo.getLangKey());
        lqw.eq(StringUtils.isNotBlank(bo.getExactLangKey()), BizLocaleMessages::getLangKey, bo.getExactLangKey());
        lqw.like(StringUtils.isNotBlank(bo.getLangValue()), BizLocaleMessages::getLangValue, bo.getLangValue());
        lqw.like(StringUtils.isNotBlank(bo.getLangKeyDesc()), BizLocaleMessages::getLangKeyDesc, bo.getLangKeyDesc());
        lqw.ge(StringUtils.isNotBlank(bo.getStartDate()), BizLocaleMessages::getCreateTime, bo.getStartDate());
        lqw.le(StringUtils.isNotBlank(bo.getEndDate()), BizLocaleMessages::getCreateTime, bo.getEndDate());
        lqw.groupBy(BizLocaleMessages::getLangKey);
        lqw.orderByDesc(BizLocaleMessages::getId);
        return lqw;
    }

    /**
     * 新增翻译消息数据
     */
    @Override
    public Boolean insertByBo(BizLocaleMessagesBo bo) {
        BizLocaleMessages add = MapstructUtils.convert(bo, BizLocaleMessages.class);
        LambdaQueryWrapper<BizLocaleMessages> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizLocaleMessages::getLocale, add.getLocale())
                          .eq(BizLocaleMessages::getLangKey, add.getLangKey());
        Boolean exists = TenantHelper.ignore(() -> baseMapper.exists(lambdaQueryWrapper));
        if (exists) {
            throw new RuntimeException("该数据已存在!");
        }
        boolean flag = TenantHelper.ignore(() -> baseMapper.insert(add) > 0);
        return flag;
    }

    @Override
    public void add(Map<String, Object> bizLocaleMessagesMap) {
        List<BizLocaleMessages> bizLocaleMessagesList = new ArrayList<>();
        // 获取表头数据
        List<SysDictDataVo> dictData = iSysDictTypeService.selectDictDataByType("system_language_type");
        if (dictData.isEmpty()) {
            throw new RuntimeException("未找到字典数据");
        }
        for (SysDictDataVo sysDictDataVo : dictData) {
            for (String key : bizLocaleMessagesMap.keySet()) {
                if (sysDictDataVo.getDictValue().equals(key)) {
                    LambdaQueryWrapper<BizLocaleMessages> lambdaQueryWrapper = Wrappers.lambdaQuery();
                    lambdaQueryWrapper.eq(BizLocaleMessages::getLocale, sysDictDataVo.getDictValue())
                                      .eq(BizLocaleMessages::getLangKey, bizLocaleMessagesMap.get("langKey"));
                    Boolean exists = TenantHelper.ignore(() -> baseMapper.exists(lambdaQueryWrapper));
                    if (exists) {
                        throw new RuntimeException("数据已存在: " + bizLocaleMessagesMap);
                    }
                    BizLocaleMessages bizLocaleMessages = new BizLocaleMessages();
                    bizLocaleMessages.setLocale(sysDictDataVo.getDictValue());
                    String langKey = (String) bizLocaleMessagesMap.get("langKey");
                    if (!isAlphaAndMaxLength(langKey)) {
                        throw new RuntimeException("标识字段由大小写英文字母数字_.组成,不能以.开头和结尾最大支持100个英文字母: " + langKey);
                    }
                    bizLocaleMessages.setLangKey(langKey);
                    String langKeyDesc = (String) bizLocaleMessagesMap.get("langKeyDesc");
                    if (null != langKeyDesc && langKeyDesc.length() > 50) {
                        throw new RuntimeException("标识描述最大支持50个字符: " + langKeyDesc);
                    }
                    bizLocaleMessages.setLangKeyDesc(langKeyDesc);
                    String langValue = (String) bizLocaleMessagesMap.get(sysDictDataVo.getDictValue());
                    if (key.equals("zh_CN")) {
                        if(StringUtils.isEmpty(langValue)){
                            throw new RuntimeException("中文必填，不可为空!");
                        }
                    }
                    if (null != langValue && langValue.length() > 200) {
                        throw new RuntimeException("字段最大支持200个字符: " + langValue);
                    }
                    bizLocaleMessages.setLangValue(langValue);
                    bizLocaleMessages.setCreateTime(new Date());
                    bizLocaleMessages.setCreateBy(LoginHelper.getUserId());
                    bizLocaleMessagesList.add(bizLocaleMessages);
                    break;
                }
            }
        }
        if (CollUtil.isNotEmpty(bizLocaleMessagesList)) {
            TenantHelper.ignore(() -> baseMapper.insertBatch(bizLocaleMessagesList));
        }
    }

    /**
     * 修改翻译消息数据
     */
    @Override
    public Boolean updateByBo(BizLocaleMessagesBo bo) {
        BizLocaleMessages update = MapstructUtils.convert(bo, BizLocaleMessages.class);
        LambdaQueryWrapper<BizLocaleMessages> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizLocaleMessages::getLocale, update.getLocale())
                          .eq(BizLocaleMessages::getLangKey, update.getLangKey());
        lambdaQueryWrapper.ne(BizLocaleMessages::getId, update.getId());
        Boolean exists = TenantHelper.ignore(() -> baseMapper.exists(lambdaQueryWrapper));
        if (exists) {
            throw new RuntimeException("该数据已存在!");
        }
        update.setUpdateTime(new Date());
        update.setUpdateBy(LoginHelper.getUserId());
        return TenantHelper.ignore(() -> baseMapper.updateById(update) > 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Map<String, Object> bizLocaleMessagesMap) {
        List<BizLocaleMessages> bizLocaleMessagesList = new ArrayList<>();
        // 获取表头数据
        List<SysDictDataVo> dictData = iSysDictTypeService.selectDictDataByType("system_language_type");
        if (dictData.isEmpty()) {
            throw new RuntimeException("未找到字典数据");
        }
        for (SysDictDataVo sysDictDataVo : dictData) {
            for (String key : bizLocaleMessagesMap.keySet()) {
                if (sysDictDataVo.getDictValue().equals(key)) {
                    BizLocaleMessages bizLocaleMessages = new BizLocaleMessages();
                    bizLocaleMessages.setLocale(sysDictDataVo.getDictValue());
                    String langKey = (String) bizLocaleMessagesMap.get("langKey");
                    if (!isAlphaAndMaxLength(langKey)) {
                        throw new RuntimeException("标识字段由大小写英文字母数字_.组成；最大支持100个英文字母: " + langKey);
                    }
                    bizLocaleMessages.setLangKey(langKey);
                    String langKeyDesc = (String) bizLocaleMessagesMap.get("langKeyDesc");
                    if (null != langKeyDesc && langKeyDesc.length() > 50) {
                        throw new RuntimeException("标识描述最大支持50个字符: " + langKeyDesc);
                    }
                    bizLocaleMessages.setLangKeyDesc(langKeyDesc);
                    String langValue = (String) bizLocaleMessagesMap.get(sysDictDataVo.getDictValue());
//                    if (key.equals("zh_CN")) {
                    if (null != langValue && langValue.length() > 200) {
                        throw new RuntimeException("字段最大支持50个字符: " + langValue);
                    }
//                    } else {
//                        if (null != langValue && langValue.length() > 200) {
//                            throw new RuntimeException("字段最大支持200个字符: " + langValue);
//                        }
//                    }
                    bizLocaleMessages.setLangValue(langValue);
                    bizLocaleMessages.setCreateTime(new Date());
                    bizLocaleMessages.setCreateBy(LoginHelper.getUserId());
                    bizLocaleMessages.setUpdateTime(new Date());
                    bizLocaleMessages.setUpdateBy(LoginHelper.getUserId());
                    bizLocaleMessagesList.add(bizLocaleMessages);
                    break;
                }
            }
        }
        if (CollUtil.isNotEmpty(bizLocaleMessagesList)) {
            List<String> langKeyList = bizLocaleMessagesList.stream().map(BizLocaleMessages::getLangKey).distinct()
                                                            .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(langKeyList)) {
                TenantHelper.ignore(() -> baseMapper.deleteByLangKeyList(langKeyList));
            }
            TenantHelper.ignore(() -> baseMapper.insertBatch(bizLocaleMessagesList));
        }
    }

    /**
     * 批量删除翻译消息数据
     */
    @Override
    public Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid) {
        TenantHelper.ignore(() -> baseMapper.updateDelById(ids, new Date(), "2", LoginHelper.getUserId(), new Date()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String langKey) {
        LambdaQueryWrapper<BizLocaleMessages> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BizLocaleMessages::getLangKey, langKey);
        List<BizLocaleMessages> bizLocaleMessagesList = TenantHelper.ignore(() -> baseMapper.selectList(lambdaQueryWrapper));
        if (CollUtil.isNotEmpty(bizLocaleMessagesList)) {
            for (BizLocaleMessages bizLocaleMessages : bizLocaleMessagesList) {
                bizLocaleMessages.setDelTime(new Date());
            }
            TenantHelper.ignore(() -> baseMapper.updateDelById(bizLocaleMessagesList.stream()
                                                                                    .map(BizLocaleMessages::getId)
                                                                                    .collect(Collectors.toList()), new Date(), "2", LoginHelper.getUserId(), new Date()));
        }

    }

    @Override
    public List<String> getExportTemplateTableHeader() {
        List<SysDictDataVo> dictData = iSysDictTypeService.selectDictDataByType("system_language_type");
        if (dictData.isEmpty()) {
            throw new RuntimeException("未找到字典数据");
        }
        List<String> headerList = new ArrayList<>();
        headerList.add("序号");
        headerList.add("标识");
        headerList.add("标识描述");
        List<String> dictLabelList = dictData.stream().map(SysDictDataVo::getDictLabel).collect(Collectors.toList());
        headerList.addAll(dictLabelList);
        return headerList;
    }

    @Override
    public List<String> getExportTableHeader() {
        List<SysDictDataVo> dictData = iSysDictTypeService.selectDictDataByType("system_language_type");
        if (dictData.isEmpty()) {
            throw new RuntimeException("未找到字典数据");
        }
        List<String> headerList = new ArrayList<>();
        headerList.add("标识");
        headerList.add("标识描述");
        List<String> dictLabelList = dictData.stream().map(SysDictDataVo::getDictLabel).collect(Collectors.toList());
        headerList.addAll(dictLabelList);
        headerList.add("创建时间");
        return headerList;
    }

    /**
     * 根据字典数据列表获取字典数据
     *
     * @param dictDataList
     * @param dictLabel
     * @param dictValue
     * @return
     */
    public static String getDictDataByDictDataList(List<SysDictDataVo> dictDataList, String dictLabel,
                                                   String dictValue) {
        if (CollUtil.isNotEmpty(dictDataList)) {
            if (StringUtils.isNotEmpty(dictLabel)) {
                return dictDataList.stream().filter(dictData -> dictData.getDictLabel().equals(dictLabel)).findFirst()
                                   .orElse(null).getDictValue();
            }
            if (StringUtils.isNotEmpty(dictValue)) {
                return dictDataList.stream().filter(dictData -> dictData.getDictValue().equals(dictValue)).findFirst()
                                   .orElse(null).getDictLabel();
            }
        }
        return null;
    }

    public static String convertMapToJsString3(Map<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        sb.append('{');
        setJsObjectString(map, sb, 0);
        // 去掉最后的", "，如果存在的话
        if (sb.length() > 1) {
            sb.setLength(sb.length() - 2);
        }
        sb.append('}');
        return sb.toString();
    }

    private static void setJsObjectString(Map<String, Object> map, StringBuilder sb, int depth) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append('"').append(entry.getKey()).append("\": ");
            Object value = entry.getValue();
            if (value instanceof Map) {
                sb.append('{');
                setJsObjectString((Map<String, Object>) value, sb, depth + 1);
                // 去掉最后一个", "，如果存在的话
                if (sb.charAt(sb.length() - 2) == ',') {
                    sb.setLength(sb.length() - 2);
                }
                sb.append('}');
            } else {
                if (value == null) {
                    sb.append("null");  // 如果值为null，用"null"表示
                } else {
                    sb.append('"').append(escapeJsonString(value.toString())).append('"');
                }
            }
            sb.append(", ");
        }
    }

    // 处理JSON字符串中的特殊字符转义
    private static String escapeJsonString(String value) {
        // 先处理反斜杠的转义
        return value.replace("\\", "\\\\")
                    // 再处理双引号的转义
                    .replace("\"", "\\\"")
                    .replace("\b", "\\b")
                    .replace("\f", "\\f")
                    .replace("\n", "\\n")
                    .replace("\r", "\\r")
                    .replace("\t", "\\t");
    }

    private static boolean isAlphaAndMaxLength(String input) {
        if (StringUtils.isEmpty(input)) {
            return false;
        }
        // 正则表达式，匹配由任意数量的大写或小写字母和.组成的字符串
        String regex = "^(?!.*\\.\\.)(?!\\.)[A-Za-z0-9_][A-Za-z0-9_.]{0,98}[A-Za-z0-9_]$";

        // 使用matches方法来判断字符串是否符合正则表达式的模式
        return input.matches(regex);
    }

}
