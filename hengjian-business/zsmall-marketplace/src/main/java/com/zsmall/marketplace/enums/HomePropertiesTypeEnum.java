package com.zsmall.marketplace.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 首页配置类型枚举
 *
 * <AUTHOR>
 * @date 2023年8月30日
 **/
@Getter
@AllArgsConstructor
public enum HomePropertiesTypeEnum {

    /**********************************  COM  **********************************/

    HomeNoticeRibbon(101, "最上方notice ribbon", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    HomeHeroBanner(102, "Hero Banner（大banner）", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomeTopSelling(103, "TOP Selling商品", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomeNewReleased(104, "New Released商品", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomeExisting(105, "对接平台区块Existing", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            long maxLength = 6;
            long allLength = currentLength + addLength;
            return allLength <= maxLength;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomePromotionCTA(106, "Promotion CTA", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    HomeZSmallCollection(107, "ZSmall Collection", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            long maxLength = 4;
            long allLength = currentLength + addLength;
            return allLength <= maxLength;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },

    HomeCategories(108, "一级类目", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomeYouDo(109, "All You Need To Do", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },

    HomeZSmallDoes(110, "What ZSmall Do", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    HomeDropshipperBenefits(111, "The Benefits For You", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomeSupplierBenefits(112, "The Benefits For Supplier", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomePrimaryVideo(113, "主影片区块", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    HomeTestimonials(114, "Testimonials", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    HomeSNS(115, "SNS", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomeComingSoon(116, "对接平台区块ComingSoon", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            long maxLength = 6;
            long allLength = currentLength + addLength;
            return allLength <= maxLength;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    HomePopupWindow(117, "首页弹窗配置", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    HomeBottomPopupWindow(118, "底部弹窗配置", HomePropertiesDestTypeEnum.Home) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },

    ProductsSecondCategories(201, "二级类目", HomePropertiesDestTypeEnum.Products) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    ProductsBanner(202, "Products页的Banner", HomePropertiesDestTypeEnum.Products) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    CollectionsBanner(501, "Collections Banner", HomePropertiesDestTypeEnum.Collections) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            long maxLength = 5;
            long allLength = currentLength + addLength;
            return allLength <= maxLength;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CollectionsSubject(502, "Collections 主题区块", HomePropertiesDestTypeEnum.Collections) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    CollectionsSeekingFromDecorStyle(503, "Collections Seeking From Decor Style ", HomePropertiesDestTypeEnum.Collections) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    SupplierBanner(701, "Supplier Banner", HomePropertiesDestTypeEnum.Supplier) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    SupplierEffect(702, "Supplier Effect 提效", HomePropertiesDestTypeEnum.Supplier) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },

    /**********************************  CN  **********************************/

    CNTop(1001, "Top配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    CNHotSearch(1002, "热搜配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    CNBanner(1003, "Banner配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CNBannerRightAD(1004, "Banner右侧广告配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            long maxLength = 3;
            long allLength = currentLength + addLength;
            return allLength == maxLength;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CNFriendlyLink(1005, "底部友情链接配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CNSocialMedia(1006, "底部社交媒体配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CNChannel(1007, "底部渠道配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CNModule(1008, "模板配置", HomePropertiesDestTypeEnum.CNHome) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CNHomeVideo(1009, "首页视频推荐", HomePropertiesDestTypeEnum.CNHome) { //首页取视频推荐前3

        @Override
        public boolean verifyContent(List<Content> contentList) {
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },
    CNPopup(1010, "首页弹窗配置", HomePropertiesDestTypeEnum.CNHome) { //首页取视频推荐前3

        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return true;
        }
    },
    CNVideo(1101, "视频推荐", HomePropertiesDestTypeEnum.CNVideo) {
        @Override
        public boolean verifyContent(List<Content> contentList) {
            List<String> valueList = contentList.stream().map(Content::getContentValue).collect(Collectors.toList());
            for (String value : valueList) {
                if (StrUtil.isBlank(value)) {
                    return false;
                }
            }
            return true;
        }

        @Override
        public boolean addBefore(long currentLength, long addLength) {
            return true;
        }

        @Override
        public boolean allowHidden() {
            return false;
        }
    },


    ;

    public abstract boolean verifyContent(List<Content> contentList);

    public abstract boolean addBefore(long currentLength, long addLength);

    public abstract boolean allowHidden();

    private final long code;
    private final String value;
    private final HomePropertiesDestTypeEnum destTypeEnum;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        private String contentKey;
        private String contentValue;

    }

    public static HomePropertiesTypeEnum fromCode(long code) {
        for (HomePropertiesTypeEnum type : HomePropertiesTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    public static HomePropertiesTypeEnum fromDestAndName(HomePropertiesDestTypeEnum destTypeEnum, String typeName) {
        for (HomePropertiesTypeEnum type : HomePropertiesTypeEnum.values()) {
            if (ObjectUtil.equals(type.destTypeEnum, destTypeEnum) && StrUtil.equals(type.name(), typeName)) {
                return type;
            }
        }
        return null;
    }

    public static HomePropertiesTypeEnum fromName(String name) {
        for (HomePropertiesTypeEnum type : HomePropertiesTypeEnum.values()) {
            if (StrUtil.equals(type.name(), name)) {
                return type;
            }
        }
        return null;
    }
}
