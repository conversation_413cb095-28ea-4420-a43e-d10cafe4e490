package com.zsmall.order.entity.anno.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.common.util.ImagesUtil;
import com.zsmall.common.util.PDFUtil;
import com.zsmall.order.entity.anno.annotaion.OptAttachmentCheck;
import com.zsmall.order.entity.domain.OrderAttachment;
import com.zsmall.order.entity.domain.OrderLogisticsInfo;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderUpdateBo;
import com.zsmall.order.entity.iservice.IOrderAttachmentService;
import com.zsmall.order.entity.iservice.IOrderLogisticsInfoService;
import com.zsmall.order.entity.iservice.IOrdersService;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/12/2 12:01
 */
@Aspect
@Component
@RequiredArgsConstructor
public class OptAttachmentAspect {
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrdersService iOrdersService;
    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 1. 查已经上传的附件
     * 2. ShippingLabelle 附件的pdf页数是否和商品数量一致,上传的附件+已有的附件分页数量要小于或等于商品数量
     * 3. CartonLabel 附件的数量要和商品数量一致? 数量只允许一个
     **/
    @Before("@annotation(optAttachmentCheck)")
    public void before(JoinPoint joinPoint, OptAttachmentCheck optAttachmentCheck) {
        Object[] args = joinPoint.getArgs();
        int pageSize = 0;
        for (Object arg : args) {
            if (arg instanceof OrderUpdateBo) {
                pageSize = uploadLabelCheck((OrderUpdateBo) arg, pageSize);
                // attachments 转换为map,key是 OrderAttachmentTypeEnum ,value 是List<OrderAttachment>
            }
//            if (arg instanceof OrderPayBo) {
//                payOrderLabelCheck(arg);
//            }
        }


    }

//    private void payOrderLabelCheck(OrderPayBo arg, int pageSize) {
//        String orderNo = arg.getOrderNo();
//        Orders order = iOrdersService.getByOrderNo(orderNo);
//        Integer totalQuantity = order.getTotalQuantity();
//        OrderAttachmentTypeEnum fileTypeEnum = arg.getFileTypeEnum();
//        MultipartFile multipartFile = arg.getMultipartFile();
//        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
//        LambdaQueryWrapper<OrderAttachment> wrapper = new LambdaQueryWrapper<OrderAttachment>().eq(OrderAttachment::getOrderNo, orderNo)
//                                                                                               .eq(OrderAttachment::getDelFlag, 0);
//        List<OrderAttachment> attachments = iOrderAttachmentService.list(wrapper);
//        if (CollUtil.isNotEmpty(attachments)) {
//            Map<OrderAttachmentTypeEnum, List<OrderAttachment>> typeEnumListMap = attachments.stream()
//                                                                                             .collect(Collectors.groupingBy(OrderAttachment::getAttachmentType));
//            boolean notEmpty = CollUtil.isNotEmpty(typeEnumListMap);
//            // todo 校验附件pdf页数是否和商品数量一致
//            if (ObjectUtil.isNotNull(orderLogisticsInfo) && CarrierTypeEnum.LTL.name()
//                                                                               .equals(orderLogisticsInfo.getLogisticsCarrierCode())) {
//
//            } else {
//
//                if (ObjectUtil.isNotEmpty(fileTypeEnum)) {
//                    if (OrderAttachmentTypeEnum.CartonLabel.equals(fileTypeEnum)) {
//                        if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.CartonLabel)) {
//                            throw new RuntimeException("CartonLabel attachment already exists!");
//                        }
//                    }
//                    if (OrderAttachmentTypeEnum.PalletLabel.equals(fileTypeEnum)) {
//                        if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.PalletLabel)) {
//                            throw new RuntimeException("PalletLabel attachment already exists!");
//                        }
//                    }
//                    if (OrderAttachmentTypeEnum.ItemLabel.equals(fileTypeEnum)) {
//                        if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ItemLabel)) {
//                            throw new RuntimeException("ItemLabel attachment already exists!");
//                        }
//                    }
//                }
//                if (OrderAttachmentTypeEnum.ShippingLabel.equals(fileTypeEnum)) {
//                    // 已经上传的附件数量
//                    List<OrderAttachment> orderAttachmentList = typeEnumListMap.get(OrderAttachmentTypeEnum.ShippingLabel);
//                    if (CollUtil.isNotEmpty(orderAttachmentList)) {
//                        for (OrderAttachment orderAttachment : orderAttachmentList) {
//                            String attachmentShowUrl = orderAttachment.getAttachmentShowUrl();
//                            InputStream oldShippingLabel = null;
//                            try {
//                                oldShippingLabel = ImagesUtil.downloadPdfAsStream(attachmentShowUrl);
//                            } catch (Exception e) {
//                                throw new RuntimeException("File download failure!");
//                            }
//
//                            List<String> oldBase64List = null;
//                            try {
//                                oldBase64List = PDFUtil.splitPdfToBase64(oldShippingLabel, 1);
//                                pageSize = pageSize + oldBase64List.size();
//                            } catch (Exception e) {
//                                throw new RuntimeException("Failed to split the PDF file!");
//                            }
//                        }
//                        if (pageSize > totalQuantity) {
//                            throw new RuntimeException("The number of pages of the ShippingLabel attachment is inconsistent with the number of goods!");
//                        }
//                        InputStream newShippingLabel;
//                        List<String> newBase64List = null;
//                        try {
//                            newShippingLabel = multipartFile.getInputStream();
//                        } catch (IOException e) {
//                            throw new RuntimeException(e);
//                        }
//                        try {
//                            newBase64List = PDFUtil.splitPdfToBase64(newShippingLabel, 1);
//                            pageSize = pageSize + newBase64List.size();
//                        } catch (Exception e) {
//                            throw new RuntimeException("Failed to split the PDF file!");
//                        }
//                        if (pageSize > totalQuantity) {
//                            throw new RuntimeException("The number of pages of the ShippingLabel attachment is inconsistent with the number of goods!");
//                        }
//                    }
//
//                }
//            }
//        }
//
//    }

    private int uploadLabelCheck(OrderUpdateBo arg, int pageSize) {
        String orderNo = arg.getOrderNo();
        Orders order = iOrdersService.getByOrderNo(orderNo);
        Integer totalQuantity = order.getTotalQuantity();
        OrderAttachmentTypeEnum fileTypeEnum = arg.getFileTypeEnum();
        MultipartFile multipartFile = arg.getMultipartFile();
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
        LambdaQueryWrapper<OrderAttachment> wrapper = new LambdaQueryWrapper<OrderAttachment>().eq(OrderAttachment::getOrderNo, orderNo)
                                                                                               .eq(OrderAttachment::getDelFlag, 0);
        List<OrderAttachment> attachments = iOrderAttachmentService.list(wrapper);
        if (CollUtil.isNotEmpty(attachments)) {
            Map<OrderAttachmentTypeEnum, List<OrderAttachment>> typeEnumListMap = attachments.stream()
                                                                                             .collect(Collectors.groupingBy(OrderAttachment::getAttachmentType));
            boolean notEmpty = CollUtil.isNotEmpty(typeEnumListMap);
            // todo 校验附件pdf页数是否和商品数量一致
            if (ObjectUtil.isNotNull(orderLogisticsInfo) && CarrierTypeEnum.LTL.name()
                                                                               .equals(orderLogisticsInfo.getLogisticsCarrierCode())) {

            } else {

                if (ObjectUtil.isNotEmpty(fileTypeEnum)) {
                    if (OrderAttachmentTypeEnum.CartonLabel.equals(fileTypeEnum)) {
                        if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.CartonLabel)) {
                            throw new RuntimeException("CartonLabel attachment already exists!");
                        }
                    }
                    if (OrderAttachmentTypeEnum.PalletLabel.equals(fileTypeEnum)) {
                        if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.PalletLabel)) {
                            throw new RuntimeException("PalletLabel attachment already exists!");
                        }
                    }
                    if (OrderAttachmentTypeEnum.ItemLabel.equals(fileTypeEnum)) {
                        if (notEmpty && typeEnumListMap.containsKey(OrderAttachmentTypeEnum.ItemLabel)) {
                            throw new RuntimeException("ItemLabel attachment already exists!");
                        }
                    }
                }
                if (OrderAttachmentTypeEnum.ShippingLabel.equals(fileTypeEnum)) {
                    // 已经上传的附件数量
                    List<OrderAttachment> orderAttachmentList = typeEnumListMap.get(OrderAttachmentTypeEnum.ShippingLabel);

                    if (CollUtil.isNotEmpty(orderAttachmentList)) {
                        for (OrderAttachment orderAttachment : orderAttachmentList) {
                            String attachmentShowUrl = orderAttachment.getAttachmentShowUrl();
                            InputStream oldShippingLabel = null;
                            try {
                                oldShippingLabel = ImagesUtil.downloadPdfAsStream(attachmentShowUrl);
                            } catch (Exception e) {
                                throw new RuntimeException("File download failure!");
                            }

                            List<String> oldBase64List = null;
                            try {
                                oldBase64List = PDFUtil.splitPdfToBase64(oldShippingLabel, 1);
                                pageSize = pageSize + oldBase64List.size();
                            } catch (Exception e) {
                                throw new RuntimeException("Failed to split the PDF file!");
                            }
                        }
                        if (pageSize > totalQuantity) {
                            throw new RuntimeException("The number of pages of the ShippingLabel attachment is inconsistent with the number of goods!");
                        }
                        InputStream newShippingLabel;
                        List<String> newBase64List = null;
                        try {
                            newShippingLabel = multipartFile.getInputStream();
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        try {
                            newBase64List = PDFUtil.splitPdfToBase64(newShippingLabel, 1);
                            pageSize = pageSize + newBase64List.size();
                        } catch (Exception e) {
                            throw new RuntimeException("Failed to split the PDF file!");
                        }
                        if (pageSize > totalQuantity) {
                            throw new RuntimeException("The number of pages of the ShippingLabel attachment is inconsistent with the number of goods!");
                        }
                    }else {
                        InputStream newShippingLabel;
                        List<String> newBase64List = null;
                        try {
                            newShippingLabel = multipartFile.getInputStream();
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        try {
                            newBase64List = PDFUtil.splitPdfToBase64(newShippingLabel, 1);
                            pageSize = pageSize + newBase64List.size();
                        } catch (Exception e) {
                            throw new RuntimeException("Failed to split the PDF file!");
                        }
                        if (pageSize > totalQuantity) {
                            throw new RuntimeException("The number of pages of the ShippingLabel attachment is inconsistent with the number of goods!");
                        }
                    }

                }
            }
        }
        return pageSize;
    }
}
