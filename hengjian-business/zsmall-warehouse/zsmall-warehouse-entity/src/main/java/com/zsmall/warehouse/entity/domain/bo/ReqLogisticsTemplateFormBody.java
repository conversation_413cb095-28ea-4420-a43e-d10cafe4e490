package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/19
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "新增-编辑物流模板参数")
public class ReqLogisticsTemplateFormBody {

    @Schema(title = "模板id,编辑时传入")
    private Long templateId;

    @Schema(title = "模板名称")
    private String templateName;

    @Schema(title = "仓库编码集合")
    private List<String> warehouseCodeList;

    @Schema(title = "服务编码")
    private String serviceCode;

    @Schema(title = "服务别名")
    private String serviceAlias;

    @Schema(title = "服务收费详情")
    private List<ReqLogisticsTemplateItemFormBody> itemAddBodyList;

}
