package com.zsmall.warehouse.entity.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/19
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "新增-编辑物流模板目的地参数")
public class ReqLogisticsTemplateShipToFormBody {

    @Schema(title = "目的地Code")
    private String shipToCode;
}
