package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 物流模板费率规则表
 * @TableName logistics_template_rate_rule
 */
@TableName(value ="logistics_template_rate_rule")
@Data
@EqualsAndHashCode(callSuper=false)
public class LogisticsTemplateRateRule extends NoDeptBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 最快送达时间
     */
    private Integer fastestDeliveryTime;

    /**
     * 最慢送达时间
     */
    private Integer slowestDeliveryTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
