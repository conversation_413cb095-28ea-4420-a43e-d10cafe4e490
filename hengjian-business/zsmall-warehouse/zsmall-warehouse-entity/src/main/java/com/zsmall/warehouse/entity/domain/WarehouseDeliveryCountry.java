package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 供应商仓库支持配送国家对象 warehouse_delivery_country
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse_delivery_country")
public class WarehouseDeliveryCountry extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 管理员仓库表主键ID
     */
    private Long warehouseId;

    /**
     * world_location主键ID
     */
    private Long worldLocationId;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 国家中文名
     */
    private String countryNameZh;

    /**
     * 国家英文名
     */
    private String countryNameEn;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;


}
