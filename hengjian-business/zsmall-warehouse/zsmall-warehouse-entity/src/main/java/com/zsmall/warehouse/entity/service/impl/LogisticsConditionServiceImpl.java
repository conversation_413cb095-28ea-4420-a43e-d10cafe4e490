package com.zsmall.warehouse.entity.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.enums.EffectiveStateType;
import com.zsmall.warehouse.entity.domain.LogisticsConditionEntity;
import com.zsmall.warehouse.entity.mapper.LogisticsConditionMapper;
import com.zsmall.warehouse.entity.service.LogisticsConditionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【logistics_condition(物流子模块表)】的数据库操作Service实现
* @createDate 2023-05-06 12:02:03
*/
@Slf4j
@Service
public class LogisticsConditionServiceImpl extends ServiceImpl<LogisticsConditionMapper, LogisticsConditionEntity>
    implements LogisticsConditionService{

    @Override
    public List<LogisticsConditionEntity> findByLogisticsIdAndStatusTypeOrderByCreateDateTimeAsc(Long id, EffectiveStateType effectiveStateType) {
        log.info("进入【根据id和状态查询物流子模板列表】方法");
        return lambdaQuery().eq(LogisticsConditionEntity::getId, id)
            .eq(LogisticsConditionEntity::getStatusType, effectiveStateType)
            .orderByAsc(LogisticsConditionEntity::getCreateTime)
            .list();
    }

    @Override
    public Boolean existsByFulfillerNameAndLogistics(String fulfiller, Long id) {
        log.info("进入【判定fulfiller名称是否存在】方法");
        LogisticsConditionEntity one = lambdaQuery().eq(LogisticsConditionEntity::getFulfillerName, fulfiller)
            .eq(LogisticsConditionEntity::getId, id)
            .one();
        return ObjectUtil.isNull(one) ? false : true;
    }
}




