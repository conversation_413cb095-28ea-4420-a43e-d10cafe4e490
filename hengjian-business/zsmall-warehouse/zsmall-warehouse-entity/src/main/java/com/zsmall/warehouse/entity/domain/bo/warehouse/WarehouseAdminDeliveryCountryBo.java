package com.zsmall.warehouse.entity.domain.bo.warehouse;


import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.warehouse.entity.domain.WarehouseAdminDeliveryCountry;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

/**
 * 仓库地址支持配送国家业务对象 warehouse_admin_delivery_country
 *
 * <AUTHOR> Li
 * @date 2024-12-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WarehouseAdminDeliveryCountry.class, reverseConvertGenerate = false)
public class WarehouseAdminDeliveryCountryBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    /**
     * 管理员仓库表主键ID
     */
    @NotNull(message = "管理员仓库表主键ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long warehouseAdminInfoId;

    /**
     * world_location主键ID
     */
    @NotNull(message = "world_location主键ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long worldLocationId;

    /**
     * 国家中文名
     */
    @NotBlank(message = "国家中文名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String countryNameZh;

    /**
     * 国家英文名
     */
    @NotBlank(message = "国家英文名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String countryNameEn;


}
