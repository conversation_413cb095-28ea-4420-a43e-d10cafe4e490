package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 仓库第三方物流商
 *
 * @TableName warehouse_third_carrier
 */
@TableName(value = "warehouse_third_carrier")
@Data
@EqualsAndHashCode(callSuper=false)
public class WarehouseThirdCarrierEntity extends SortEntity implements Serializable {

  @TableField(exist = false)
  private static final long serialVersionUID = 1L;

  /**
   * id
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 物流商名称
   */
  @TableField(value = "carrier_name")
  private String carrierName;

  /**
   * 物流商类型
   */
  @TableField(value = "carrier_type")
  private String carrierType;

  /**
   * 仓库id
   */
  @TableField(value = "warehouse_id")
  private Long warehouseId;


}
