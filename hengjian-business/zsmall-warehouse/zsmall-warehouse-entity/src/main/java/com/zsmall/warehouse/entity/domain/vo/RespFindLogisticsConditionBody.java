package com.zsmall.warehouse.entity.domain.vo;

import com.zsmall.common.domain.dto.LogisticsConditionBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-返回物流管理子模块列表
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应信息-返回物流管理子模块列表")
public class RespFindLogisticsConditionBody {

  @Schema(title = "物流子模块列表信息")
  private List<LogisticsConditionBody> logisticsItemList;

  @Schema(title = "物流模块名称")
  private String logisticsName;

  @Schema(title = "国家名称")
  private String countryName;

  @Schema(title = "国家id")
  private Long countryId;

}
