package com.zsmall.warehouse.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 仓库地址信息对象 warehouse_address
 *
 * <AUTHOR>
 * @date 2023-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "warehouse_address", autoResultMap = true)
public class WarehouseAddress extends NoDeptBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 仓库主表主键
     */
    private Long warehouseId;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 地区表主键（国家）
     */
    private Long countryId;

    /**
     * 国家名文本
     */
    private String country;

    /**
     * 地区表主键（州/省）
     */
    private Long stateId;
    /**
     * 州/省名文本
     */
    private String state;

    /**
     * 地区表主键（市县）
     */
    private Long cityId;

    /**
     * 市县名文本
     */
    private String city;

    /**
     * 详细地址1
     */
    private String address1;

    /**
     * 详细地址2
     */
    private String address2;

    /**
     * 仓库邮编
     */
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    private String managerPhone;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 纬度
     */
    private Long latitude;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
