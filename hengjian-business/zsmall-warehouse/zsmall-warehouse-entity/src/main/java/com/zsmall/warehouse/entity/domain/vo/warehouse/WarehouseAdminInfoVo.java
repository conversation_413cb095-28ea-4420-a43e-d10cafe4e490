package com.zsmall.warehouse.entity.domain.vo.warehouse;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;
import java.util.List;


/**
 * 管理端仓库信息视图对象 warehouse_admin_info
 *
 * <AUTHOR> Li
 * @date 2024-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WarehouseAdminInfo.class)
public class WarehouseAdminInfoVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 仓库类型
     */
    @ExcelProperty(value = "仓库类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "区=分自有仓库与第三方仓库")
    private Integer warehouseType;
    /**
     * 仓库类型描述
     */
    @ExcelProperty(value = "仓库类型描述")
    private String warehouseTypeName;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 仓库编号
     */
    @ExcelProperty(value = "仓库编号")
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    @ExcelProperty(value = "仓库唯一系统编号")
    private String warehouseSystemCode;

    /**
     * 仓库状态（0-停用，1-启用等）
     */
    @ExcelProperty(value = "仓库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-停用，1-启用等")
    private Integer warehouseState;

    /**
     * 是否支持第三方物流账号（0-否，1-是）
     */
    @ExcelProperty(value = "是否支持第三方物流账号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-否，1-是")
    private Integer supportLogisticsAccount;

    /**
     * 地区表主键（国家）
     */
    @ExcelProperty(value = "地区表主键", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "国=家")
    private Long countryId;

    /**
     * 国家名文本
     */
    @ExcelProperty(value = "国家名文本")
    private String country;

    /**
     * 地区表主键（州/省）
     */
    @ExcelProperty(value = "地区表主键", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "州=/省")
    private Long stateId;

    /**
     * 州/省名文本
     */
    @ExcelProperty(value = "州/省名文本")
    private String state;

    /**
     * 地区表主键（市县）
     */
    @ExcelProperty(value = "地区表主键", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "市=县")
    private Long cityId;

    /**
     * 市县名文本
     */
    @ExcelProperty(value = "市县名文本")
    private String city;

    /**
     * 详细地址1
     */
    @ExcelProperty(value = "详细地址1")
    private String address1;

    /**
     * 详细地址2
     */
    @ExcelProperty(value = "详细地址2")
    private String address2;

    /**
     * 仓库邮编
     */
    @ExcelProperty(value = "仓库邮编")
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    @ExcelProperty(value = "仓库管理者姓名")
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    @ExcelProperty(value = "仓库管理者联系电话")
    private String managerPhone;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Long longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Long latitude;

    /**
     * 配送国家ID
     */
    private List<Long> distributionCountriesId;
}
