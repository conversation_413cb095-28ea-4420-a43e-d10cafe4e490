package com.zsmall.warehouse.entity.domain.vo.shippingService;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.warehouse.entity.domain.ShippingService;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ShippingService.class)
public class ShippingServiceVo implements Serializable {

    /**
     * id
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 快递服务编码
     */
    @ExcelProperty(value = "快递服务编码")
    private String code;

    /**
     * 快递服务名称
     */
    @ExcelProperty(value = "快递服务名称")
    private String name;

    /**
     * 物流商唯一代号
     */
    @ExcelProperty(value = "物流商唯一代号")
    private String logisticsSlug;

    /**
     * 国家二位代号
     */
    @ExcelProperty(value = "国家二位代号")
    private String countryCode;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    private Integer sort;


}
