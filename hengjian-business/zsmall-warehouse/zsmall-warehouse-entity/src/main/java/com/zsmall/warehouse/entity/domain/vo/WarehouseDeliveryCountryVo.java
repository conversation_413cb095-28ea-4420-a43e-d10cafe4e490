package com.zsmall.warehouse.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.warehouse.entity.domain.WarehouseDeliveryCountry;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;



/**
 * 供应商仓库支持配送国家视图对象 warehouse_delivery_country
 *
 * <AUTHOR> Li
 * @date 2024-12-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WarehouseDeliveryCountry.class)
public class WarehouseDeliveryCountryVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 管理员仓库表主键ID
     */
    @ExcelProperty(value = "管理员仓库表主键ID")
    private Long warehouseId;

    /**
     * world_location主键ID
     */
    @ExcelProperty(value = "world_location主键ID")
    private Long worldLocationId;

    /**
     * 国家中文名
     */
    @ExcelProperty(value = "国家中文名")
    private String countryNameZh;

    /**
     * 国家英文名
     */
    @ExcelProperty(value = "国家英文名")
    private String countryNameEn;


}

