<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.LogisticsWarehouseRelationMapper">


    <select id="queryWarehouseName" resultType="java.lang.String">
        SELECT w.warehouse_name
        FROM logistics_warehouse_relation lwr
                 JOIN warehouse w ON lwr.warehouse_id = w.id
        WHERE lwr.del_flag = '0'
          AND w.del_flag = '0'
          AND lwr.logistics_template_id = #{logisticsTemplateId}
    </select>
</mapper>
