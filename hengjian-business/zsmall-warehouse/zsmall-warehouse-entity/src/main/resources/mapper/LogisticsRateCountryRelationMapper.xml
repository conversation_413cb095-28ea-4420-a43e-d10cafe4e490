<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.warehouse.entity.mapper.LogisticsRateCountryRelationMapper">

    <resultMap id="LocationNameMap" type="com.zsmall.common.domain.SqlLocaleField">
        <result property="zh_CN" column="zh_CN"/>
        <result property="en_US" column="en_US"/>
    </resultMap>


    <select id="queryLocationName" resultMap="LocationNameMap" resultType="com.zsmall.common.domain.SqlLocaleField">
        SELECT wl.location_other_name ->> '$.zh_CN' as 'zh_CN', wl.location_other_name ->> '$.en_US' as 'en_US'
        FROM logistics_rate_country_relation lrcr
                 join world_location wl ON
            lrcr.world_location_id = wl.id
        WHERE lrcr.del_flag = '0'
          AND lrcr.logistics_template_id = #{templateId}
    </select>
</mapper>
