<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.bma.open.member.mapper.MemberRuleRelationMapper">
    <update id="logicDel">
        update member_rule_relation set del_flag = 2 where id = #{id}
    </update>

    <select id="queryPageList" resultType="com.zsmall.product.entity.domain.member.MemberRuleRelation">
        select * from member_rule_relation where del_flag =0
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.ruleCustomizerTenantId)">
            and rule_customizer_tenant_id like CONCAT('%', #{queryBo.ruleCustomizerTenantId}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.ruleFollowerTenantId)">
            and rule_follower_tenant_id like CONCAT('%', #{queryBo.ruleFollowerTenantId}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.levelId)">
            AND level_id =#{queryBo.levelId}
        </if>
        order by create_time,id desc

    </select>
</mapper>
