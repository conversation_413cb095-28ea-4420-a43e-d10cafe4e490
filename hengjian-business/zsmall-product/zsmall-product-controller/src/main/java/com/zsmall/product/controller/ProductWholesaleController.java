package com.zsmall.product.controller;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.biz.service.ProductWholesaleService;
import com.zsmall.product.entity.domain.bo.product.ProductQueryBo;
import com.zsmall.product.entity.domain.bo.wholesale.GetWholesaleProductInfoBo;
import com.zsmall.product.entity.domain.vo.product.ProductListVo;
import com.zsmall.product.entity.domain.vo.wholesale.WholesaleProductBo;
import com.zsmall.product.entity.domain.vo.wholesale.WholesaleProductVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 批发商品
 *
 * <AUTHOR>
 * @date 2023/7/20 18:20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/productWholesale")
public class ProductWholesaleController {

    private final ProductWholesaleService productWholesaleService;

    /**
     * 查询商品SPU列表
     */
    @GetMapping("/list")
    public TableDataInfo<ProductListVo> list(ProductQueryBo bo, PageQuery pageQuery) {
        return productWholesaleService.queryPageList(bo, pageQuery);
    }

    /**
     * 批发商品保存
     */
    @PostMapping(value = "/saveProduct")
    public R<GetWholesaleProductInfoBo> saveProduct(@RequestBody WholesaleProductBo bo) throws Exception {
        return productWholesaleService.saveProduct(bo);
    }

    /**
     * 获取批发商品详情
     */
    @PostMapping(value = "/getWholesaleProductInfo")
    public R<WholesaleProductVo> getWholesaleProductInfo(@RequestBody GetWholesaleProductInfoBo bo) {
        return productWholesaleService.getWholesaleProductInfo(bo);
    }




}
