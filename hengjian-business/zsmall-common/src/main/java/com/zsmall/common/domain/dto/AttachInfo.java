package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/11 14:40
 */
@Setter
@Getter
@Accessors(chain = true)
public class AttachInfo {
    /**
     * 附件名称
     */
    private String name;

    /**
     * 附件url
     */
    private String url;

    /** OrderAttachmentTypeEnum
     * 订单附件类型 0:Shippinglabel ;1:Image ;2:Zip 3.
     */
    private Integer type;
    /**
     *  附件类型:Label:0 BOL:2(承运商为LTL必填)
     * */
    @JsonProperty("file_type")
    @JSONField(name = "file_type")
    private Integer fileType;
    /**
     * 备注
     */
    private String remark;

    /**
     * base64 字符串,如果填写，则不认上面url里面的内容( url 与file必填其一 )
     */
    private String file;
}
