package com.zsmall.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-sku映射信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "响应信息-sku映射信息")
public class RespSkuMappingInfoBody {

    @Schema(title = "商品SKU名称")
    private String name;

    @Schema(title = "图片展示地址")
    private String imageShowUrl;

    @Schema(title = "商品编号")
    private String productCode;

    @Schema(title = "商品sku")
    private String sku;

    @Schema(title = "映射sku")
    private String mappingSku;

    @Schema(title = "itemNo")
    private String itemNo;

    @Schema(title = "渠道店铺别名")
    private String channelAlias;

    @Schema(title = "规格集合")
    private List<String> specValueList;


    @Schema(title = "仓库地址集合")
    private List<SkuWarehouseBody> warehouseAddress;

}
