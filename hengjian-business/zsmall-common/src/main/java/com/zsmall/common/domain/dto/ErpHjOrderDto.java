package com.zsmall.common.domain.dto;

import cn.hutool.core.date.DateTime;
import lombok.Data;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/3 17:20
 */
@Data
public class ErpHjOrderDto {
    private String orderNo;
    private String carrier;
    private String carrierCode;
    private String warehouseCode;
    private String currency;
    private DateTime paidAt;
    private String shipToCountry;
    private String shipToState;
    private String shipToCity;
    private String shipToPostal;
    private String shipToAddress1;
    private String shipToAddress2;
    private String shipToContact;
    private String customerEmail;
    private String shipToTelephone;
    private String thirdPartyAccount;
    private String shipServiceLevel;
    private List<ErpHjProductItemDto> productItems;
    private List<ErpHjAttachInfoItemDto> attachInfoItems;
    // 店铺标识 thirdChannelFlag
    private String channelFlag;

    /**
     * 功能描述：添加产品项目
     *
     * @param productItems 产品项目
     * @return {@link List }<{@link ErpHjProductItemDto }>
     * <AUTHOR>
     * @date 2024/01/04
     */
    public List<ErpHjProductItemDto> addProductItems(List<ErpHjProductItemDto> productItems){
        this.productItems = productItems;
        return productItems;
    }

    /**
     * 功能描述：添加附加信息项
     *
     * @param attachInfoItems 附加信息项
     * @return {@link List }<{@link ErpHjAttachInfoItemDto }>
     * <AUTHOR>
     * @date 2024/01/04
     */
    public  List<ErpHjAttachInfoItemDto> addAttachInfoItems(List<ErpHjAttachInfoItemDto> attachInfoItems ){
        this.attachInfoItems = attachInfoItems;
        return attachInfoItems;
    }
}
