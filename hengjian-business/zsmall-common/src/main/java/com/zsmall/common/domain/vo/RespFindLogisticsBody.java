package com.zsmall.common.domain.vo;

import com.zsmall.common.domain.dto.LogisticsBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-返回物流管理主信息
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应信息-返回物流管理主信息")
public class RespFindLogisticsBody {

    @Schema(title = "物流信息")
    private List<LogisticsBody> logisticsList;

}
