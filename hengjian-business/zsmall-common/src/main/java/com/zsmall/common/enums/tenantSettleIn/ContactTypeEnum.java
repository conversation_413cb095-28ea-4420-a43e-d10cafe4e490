package com.zsmall.common.enums.tenantSettleIn;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 联系人类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ContactTypeEnum {

  /**
   * 业务联系人
   */
  BusinessContact(1, "业务联系人", "Business Contact"),

  /**
   * 财务联系人
   */
  FinancialContact(2, "财务联系人", "Financial Contact"),

  /**
   * 售后联系人
   */
  SalesContact(3, "售后联系人", "Sales Contact"),


  ;

  private final int code;
  private final String textCn;
  private final String textEn;

  public static ContactTypeEnum fromCode(Integer code) {
    for (ContactTypeEnum contactTypeEnum : ContactTypeEnum.values()) {
      if (code == contactTypeEnum.getCode()) {
        return contactTypeEnum;
      }
    }
    return null;
  }

  public static ContactTypeEnum fromName(String name) {
    for (ContactTypeEnum contactTypeEnum : ContactTypeEnum.values()) {
      if (StrUtil.equals(name, contactTypeEnum.name())) {
        return contactTypeEnum;
      }
    }
    return null;
  }
}
