package com.zsmall.common.enums;

import cn.hutool.core.util.NumberUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 运算符号枚举
 */
@Getter
@AllArgsConstructor
public enum OperationSymbolEnum {

    /**
     * 加
     */
    Add(1, "+"),
    /**
     * 乘
     */
    multiply(2, "*");


    private final int code;
    private final String value;

    public static String fromCode(int code) {
        for (OperationSymbolEnum symbolEnum : OperationSymbolEnum.values()) {
            if (symbolEnum.code == code) {
                return symbolEnum.value;
            }
        }
        return null;
    }

    public static BigDecimal calculate(int code, BigDecimal value1, BigDecimal value2) {
        for (OperationSymbolEnum symbolEnum : OperationSymbolEnum.values()) {
            if (symbolEnum.code == code) {
                switch (symbolEnum.getValue()) {
                    case "+":
                        return NumberUtil.add(value1, value2);
                    case "*":
                        return NumberUtil.mul(value1, value2);
                }
            }
        }
        return NumberUtil.toBigDecimal(0);
    }
}
