package com.zsmall.common.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/30 11:02
 */
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@SuperBuilder
@Getter
@Setter
public class SearchProductVO extends SearchReqBody {

    @JsonProperty("page_size")
    @JSONField(name="page_size")
    private String pageSize;

    @JsonProperty("page_token")
    @JSONField(name="page_token")
    private String pageToken;
}
