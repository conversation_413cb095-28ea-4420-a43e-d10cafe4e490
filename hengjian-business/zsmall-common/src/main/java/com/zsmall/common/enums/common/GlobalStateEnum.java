package com.zsmall.common.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 全局有效状态枚举
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
@Getter
@AllArgsConstructor
public enum GlobalStateEnum implements IEnum<Integer> {

    /**
     * 有效、启用等
     */
    Valid(1),

    /**
     * 无效、停用等
     */
    Invalid(0),

    ;

    private Integer code;

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return code;
    }

    public static GlobalStateEnum fromCode(Integer code) {
        return Arrays.stream(GlobalStateEnum.values()).filter(item -> item.getCode() == code).findFirst().get();
    }
}
