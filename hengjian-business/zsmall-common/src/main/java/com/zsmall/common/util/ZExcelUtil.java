package com.zsmall.common.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.cell.CellUtil;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.exception.ExcelMessageException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * Excel工具类
 *
 * <AUTHOR>
 * @date 2023/1/12
 */
@Slf4j
public class ZExcelUtil {

    /**
     * 表格数据转实体类
     *
     * @param excelReader
     * @param beanClass
     * @param startRow    从零开始
     * @param <T>
     * @return
     * @throws ExcelMessageException
     */
    public static <T> List<T> parseFieldDTO(ExcelReader excelReader, Class<T> beanClass, int startRow)
        throws ExcelMessageException {
        int rowCount = excelReader.getRowCount();
        int columnCount = excelReader.getColumnCount();

        List<T> list = new ArrayList<>();
        LocaleMessage localeMessage = new LocaleMessage();
        for (int nowRow = startRow; nowRow < rowCount; nowRow++) {
            if (isBlankRow(excelReader, nowRow)) {
                continue; // 如果是空白行，则跳过
            }
            int showRowIndex = nowRow + 1;

            try {
                T newInstance = ReflectUtil.newInstance(beanClass);
                ReflectUtil.setFieldValue(newInstance, "rowIndex", nowRow);
                ReflectUtil.setFieldValue(newInstance, "showRowIndex", showRowIndex);

                for (int nowColumn = 0; nowColumn < columnCount; nowColumn++) {
                    Cell cell = excelReader.getCell(nowColumn, nowRow);
                    int columnIndex = nowColumn + 1;
                    log.info("columnIndex = {}", columnIndex);
                    // Field[] fields = ReflectUtil.getFields(beanClass,
                    //     field -> NumberUtil.compare(AnnotationUtil.getAnnotationValue(field, ExcelFieldAnnotation.class, "columnIndex"), columnIndex) == 0);
                    Field[] fields = ReflectUtil.getFields(beanClass);
                    Field field = fields[nowColumn];
                    ExcelFieldAnnotation annotation = field.getAnnotation(ExcelFieldAnnotation.class);
                    String column = annotation.column();

                    boolean required = annotation.required();
                    Class<?> fieldType = field.getType();
                    try {
                        Object cellValue = getCellValueByType(fieldType, cell);
                        if (required && ObjectUtil.isEmpty(cellValue)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, column));
                        }
                        ReflectUtil.setFieldValue(newInstance, field, cellValue);
                    } catch (ExcelMessageException e) {
                        throw e;
                    } catch (IllegalArgumentException e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    } catch (Exception e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    }
                }
                list.add(newInstance);
            } catch (ExcelMessageException e) {
                log.error("parseFieldDTO ExcelStatusCodeException {}", e.getMessage(), e);
                localeMessage.appendSurround(e.getLocaleMessage(), "<p>", "</p></br>");
            } catch (Exception e) {
                log.error("parseFieldDTO error {}", e.getMessage(), e);
            }
        }

        if (localeMessage.hasData()) {
            throw new ExcelMessageException(localeMessage);
        }
        return list;
    }

    /**
     * 功能描述：空白行-列过滤
     *
     * @param excelReader excel阅读器
     * @param rowIdx      行idx
     * @return boolean
     * <AUTHOR>
     * @date 2024/04/01
     */
    private static boolean isBlankRow(ExcelReader excelReader, int rowIdx) {
        Row row = excelReader.getOrCreateRow(rowIdx);
        if (row == null) {
            return true;
        }
        for (Cell cell : row) {
            if (cell != null && !cell.getCellType().equals(CellType.BLANK)) {
                return false;
            }
        }
        return true;
    }
    /**
     * 定位最后一个有效行的索引（解决尾部大量空行问题）
     *
     * @param sheet     Excel 工作表对象
     * @param startRow  起始扫描行号（0-based）
     * @return 最后一个非空行的索引（若未找到返回 startRow）
     */
    private static int findLastValidRow(Sheet sheet, int startRow) {
        // 参数校验
        if (sheet == null || startRow < 0) return startRow;

        // 配置扫描参数（根据文件大小动态调整）
        final int BASE_STEP = Math.max(50000, sheet.getLastRowNum() / 20); // 动态基础步长
        final int FINE_SCAN_THRESHOLD = 1000;   // 精细扫描阈值
        final int MAX_EMPTY_STREAK = 100;        // 连续空行中断阈值

        int rowPointer = startRow;
        int lastValidRow = startRow;
        int emptyStreak = 0;
        int step = BASE_STEP;
        int lastRowNum = sheet.getLastRowNum();
        int maxScanAttempts = lastRowNum * 2; // 最大扫描次数=2倍总行数
        int scanCount = 0;
        while (rowPointer <= lastRowNum && scanCount++ < maxScanAttempts) {
            Row currentRow = sheet.getRow(rowPointer);

            // 关键优化1：快速空行检测
            if (!isRowEmpty(currentRow)) {
                lastValidRow = rowPointer; // 更新最后有效行
                emptyStreak = 0;           // 重置空行计数器

                // 发现有效行后进入精细扫描模式
                if (step > FINE_SCAN_THRESHOLD) {
                    rowPointer = Math.max(startRow, rowPointer - step);
                    step = Math.max(1, step / 10);
                    continue;
                }
                if (rowPointer < 0 || rowPointer > lastRowNum) {
                    break;
                }
            } else {
                if (++emptyStreak > MAX_EMPTY_STREAK) break; // 连续空行超阈值则终止
            }

            // 关键优化2：动态步长调整
            if (step < FINE_SCAN_THRESHOLD && emptyStreak > 10) {
                step = Math.min(BASE_STEP, step * 5); // 扩大步长加速跳过空行区
            }

            rowPointer += step;

            // 边界保护：防止越界
            if (rowPointer > sheet.getLastRowNum()) {
                rowPointer = sheet.getLastRowNum();
                if (isRowEmpty(sheet.getRow(rowPointer))) break;
            }
        }

        // 精确定位：在最后区间逐行确认
        for (int i = lastValidRow; i <= Math.min(lastValidRow + MAX_EMPTY_STREAK, sheet.getLastRowNum()); i++) {
            if (!isRowEmpty(sheet.getRow(i))) {
                lastValidRow = i;
            }
        }
        return lastValidRow+1;
    }
    /**
     * 表格数据转实体类
     *
     * @param excelReader
     * @param beanClass
     * @param titleRowIdx 标题所在行（下标从0开始）
     * @param startRow    从第几行开始读取数据（下标从0开始）
     * @param <T>
     * @return ExcelMessageException
     */
    public static <T> List<T> parseFieldDTO(ExcelReader excelReader, Class<T> beanClass, int titleRowIdx, int startRow)
        throws ExcelMessageException {
        Sheet sheet = excelReader.getSheet();
        // 从最大行号向前跳跃扫描
        int lastValidRow = findLastValidRow(sheet, startRow);
        int columnCount = excelReader.getColumnCount();

        List<T> list = new ArrayList<>();
        LocaleMessage localeMessage = new LocaleMessage();
        Row titleRow = excelReader.getOrCreateRow(titleRowIdx);
        // 需要额外对row进行过滤校验

        for (int nowRow = startRow; nowRow < lastValidRow; nowRow++) {
            if (isBlankRow(excelReader, nowRow)) {
                continue; // 如果是空白行，则跳过
            }
            int showRowIndex = nowRow + 1;

            try {
                T newInstance = ReflectUtil.newInstance(beanClass);
                ReflectUtil.setFieldValue(newInstance, "rowIndex", nowRow);
                ReflectUtil.setFieldValue(newInstance, "showRowIndex", showRowIndex);

                for (int nowColumn = 0; nowColumn < columnCount; nowColumn++) {
                    Cell cell = excelReader.getCell(nowColumn, nowRow);
                    int columnIndex = nowColumn + 1;
                    log.info("columnIndex = {}", columnIndex);
                    Field[] fields = ReflectUtil.getFields(beanClass);
                    Field field = fields[nowColumn];
                    ExcelFieldAnnotation annotation = field.getAnnotation(ExcelFieldAnnotation.class);

                    Cell titleCell = titleRow.getCell(nowColumn);
                    String column = "";
                    if (titleCell != null) {
                        column = titleCell.getStringCellValue();
                    }

                    boolean required = false;
                    if (annotation != null) {
                        required = annotation.required();
                    }

                    Class<?> fieldType = field.getType();
                    try {
                        Object cellValue = getCellValueByType(fieldType, cell);
                        if (required && ObjectUtil.isEmpty(cellValue)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, column));
                        }
                        ReflectUtil.setFieldValue(newInstance, field, cellValue);
                    } catch (ExcelMessageException e) {
                        throw e;
                    } catch (IllegalArgumentException e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    } catch (Exception e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    }
                }
                list.add(newInstance);
            } catch (ExcelMessageException e) {
                // 异常源头
                log.error("parseFieldDTO ExcelStatusCodeException {}", e.getMessage(), e);
                localeMessage.appendSurround(e.getLocaleMessage(), "<p>", "</p></br>");
            } catch (Exception e) {
                log.error("parseFieldDTO error {}", e.getMessage(), e);
            }
        }

        if (localeMessage.hasData()) {
            throw new ExcelMessageException(localeMessage);
        }
        return list;
    }
    private static boolean isRowEmpty(Row row) {
        if (row == null) return true;
        // 关键：仅检查有物理存储的单元格
        for (int i = row.getFirstCellNum(); i <= row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }
    /**
     * 功能描述：解析字段dto v2 会员产品专属
     *
     * @param excelReader excel阅读器
     * @param beanClass   bean类
     * @param titleRowIdx 标题行idx
     * @param startRow    起始行
     * @return {@link List }<{@link T }>
     * <AUTHOR>
     * @date 2024/07/10
     */
    public static <T> List<T> parseFieldDTOV2(ExcelReader excelReader, Class<T> beanClass, int titleRowIdx,
                                              int startRow)
        throws ExcelMessageException {
        int rowCount = excelReader.getRowCount();
        int columnCount = excelReader.getColumnCount();

        List<T> list = new ArrayList<>();
        LocaleMessage localeMessage = new LocaleMessage();
        Row titleRow = excelReader.getOrCreateRow(titleRowIdx);
        Row titleRowForPrice = excelReader.getOrCreateRow(0);
        // 这里只是一行的数据,需要拿到56 59 62 +3 的下标的数据的名称的前两位
        Map<Integer, Object> cellMap = new HashMap<>();
        // 从56开始解析
        for (int i = 56; i < titleRow.getLastCellNum(); i++) {
            if(i == 56 || (i - 56) % 3 == 0){
                Cell cell = titleRowForPrice.getCell(i);
                if (cell != null) {
                    Object cellValue = CellUtil.getCellValue(cell);
                    cellMap.put(i, cellValue);

                }
            }

        }
        // 需要额外对row进行过滤校验
        // 后续不能从第10行开始,要从第1行开始但是跳过前9行
        for (int nowRow = startRow; nowRow < rowCount; nowRow++) {
            List<JSONObject> extendedColumn = new ArrayList<>();
            if (isBlankRow(excelReader, nowRow)) {
                continue; // 如果是空白行，则跳过
            }
            int showRowIndex = nowRow + 1;

            try {
                T newInstance = ReflectUtil.newInstance(beanClass);
                ReflectUtil.setFieldValue(newInstance, "rowIndex", nowRow);
                ReflectUtil.setFieldValue(newInstance, "showRowIndex", showRowIndex);
                // 把titleRow 的下标大于=56的所有的cell都拿出来放进map内


                for (int nowColumn = 0; nowColumn < columnCount; nowColumn++) {
                    if (nowColumn==56){
                        log.info("nowColumn = {}", nowColumn);
                    }
                    Cell cell = excelReader.getCell(nowColumn, nowRow);
                    int columnIndex = nowColumn + 1;
                    log.info("columnIndex = {}", columnIndex);
                    Field[] fields = ReflectUtil.getFields(beanClass);
                    Field field;
                    if(nowColumn>=fields.length){
                        field = fields[fields.length-2];
                    }else{
                        field = fields[nowColumn];
                    }

                    ExcelFieldAnnotation annotation = field.getAnnotation(ExcelFieldAnnotation.class);

                    Cell titleCell = titleRow.getCell(nowColumn);
                    String column = "";
                    if (titleCell != null) {
                        column = titleCell.getStringCellValue();
                    }

                    boolean required = false;
                    if (annotation != null) {
                        required = annotation.required();
                    }

                    Class<?> fieldType = field.getType();
                    try {
                        Object cellValue = getCellValueByType(fieldType, cell);
                        if (required && ObjectUtil.isEmpty(cellValue)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, column));
                        }
                        // 实际要把56后续的所有的
                        // 如果是list ReflectUtil.setFieldValue(newInstance, field, cellValue); 额外处理存一个集合,其他的正常
                        // 大于等于56 同时满足 是56 +3的倍数

                        if (nowColumn == 56) {
                            // 一次性把数据放完整 一列的 所有金额相关
                            // 56~59 62~65 68~71 71~N
//                          4个属性填充 为一个jsonObject
                            for (int i = nowColumn; i < titleRow.getLastCellNum(); i++) {
                                //
                                if(i == 56 || (i - 56) % 3 == 0){
                                    JSONObject json = new JSONObject();
                                    for (int z = 0; z < 4; z++) {
                                        if(i==56||i==59){
                                            log.info("i = {}", i);
                                        }                                        // 遍历 其实还需要存等级
                                        Field fieldPrice = fields[nowColumn];
                                        Class<?> fieldType1 = fieldPrice.getType();
                                        Cell cellPrice = excelReader.getCell(i+z, nowRow);
                                        Object price = getCellValueByType(fieldType1, cellPrice);
                                        if (z == 0) {
                                            json.put("unitPrice",price);
                                        }
                                        if (z == 1) {
                                            json.put("operationFee", price);
                                        }
                                        if (z == 2) {
                                            json.put("finalDeliveryFee", price);
                                        }
                                        if (z == 3) {
                                            Object levelName = cellMap.get(i);
                                            if (ObjectUtil.isNotEmpty(levelName)){
                                                json.put("levelName", levelName);
                                            }else {
                                                throw new Exception("等级名称不能为空");
                                            }

                                        }

                                    }
                                    extendedColumn.add(json);
                                }

                            }
                        }
                        if("priceJSON".equals(field.getName())){
                            ReflectUtil.setFieldValue(newInstance, field, extendedColumn);
                        }else{
                            ReflectUtil.setFieldValue(newInstance, field, cellValue);
                        }
                    } catch (ExcelMessageException e) {
                        throw e;
                    } catch (IllegalArgumentException e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    } catch (Exception e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    }
                }
                list.add(newInstance);
            } catch (ExcelMessageException e) {
                // 异常源头
                log.error("parseFieldDTO ExcelStatusCodeException {}", e.getMessage(), e);
                localeMessage.appendSurround(e.getLocaleMessage(), "<p>", "</p></br>");
            } catch (Exception e) {
                log.error("parseFieldDTO error {}", e.getMessage(), e);
            }
        }

        if (localeMessage.hasData()) {
            throw new ExcelMessageException(localeMessage);
        }
        return list;
    }

    // 新增方法，用于移除指定名称的字段
    public static Field[] removeFieldsByName(Field[] fields, List<String> removeNames) {
        List<Field> filteredFields = new ArrayList<>();
        for (Field field : fields) {
            if (!removeNames.contains(field.getName())) {
                filteredFields.add(field);
            }
        }
        // 将ArrayList转换为数组
        return filteredFields.toArray(new Field[0]);
    }
    /**
     * 功能描述：解析字段dto v2 会员产品专属
     *
     * @param excelReader excel阅读器
     * @param beanClass   bean类
     * @param titleRowIdx 标题行idx
     * @param startRow    起始行
     * @return {@link List }<{@link T }>
     * <AUTHOR>
     * @date 2024/07/10
     */
    public static <T> List<T> parseFieldDTOV3(ExcelReader excelReader, Class<T> beanClass, int titleRowIdx, int startRow,int startCol,int fieldSpacing) throws ExcelMessageException {
        int rowCount = excelReader.getRowCount();
        int columnCount = excelReader.getColumnCount();

        List<T> list = new ArrayList<>();
        LocaleMessage localeMessage = new LocaleMessage();
        Row titleRow = excelReader.getOrCreateRow(titleRowIdx);
        Row titleRowForPrice = excelReader.getOrCreateRow(0);
        // 这里只是一行的数据,需要拿到n +3 的下标的数据的名称的前两位
        Map<Integer, Object> cellMap = new HashMap<>();
        // 从1开始解析 从那一列开始解析就是
        for (int i = startCol; i < titleRow.getLastCellNum(); i++) {
            if(i == startCol || (i - startCol) % fieldSpacing == 0){
                Cell cell = titleRowForPrice.getCell(i);
                if (cell != null) {
                    Object cellValue = CellUtil.getCellValue(cell);
                    cellMap.put(i, cellValue);

                }
            }

        }
        // 需要额外对row进行过滤校验
        for (int nowRow = startRow; nowRow < rowCount; nowRow++) {
            List<JSONObject> extendedColumn = new ArrayList<>();
            if (isBlankRow(excelReader, nowRow)) {
                continue; // 如果是空白行，则跳过
            }
            int showRowIndex = nowRow + 1;

            try {
                T newInstance = ReflectUtil.newInstance(beanClass);
                ReflectUtil.setFieldValue(newInstance, "rowIndex", nowRow);
                ReflectUtil.setFieldValue(newInstance, "showRowIndex", showRowIndex);

                for (int nowColumn = 0; nowColumn < columnCount; nowColumn++) {
                    if (nowColumn==1){
                        log.info("nowColumn = {}", nowColumn);
                    }
                    Cell cell = excelReader.getCell(nowColumn, nowRow);
                    int columnIndex = nowColumn + 1;
                    log.info("columnIndex = {}", columnIndex);
                    Field[] fields = ReflectUtil.getFields(beanClass);
                    Field field;
                    // fields 需要移除name为 unitPrice operationFee finalDeliveryFee 的元素
                    List<String> removeFieldNames = Arrays.asList("unitPrice", "originalFinalDeliveryFee", "originalOperationFee");
                //    List<String> removeFieldNames = Arrays.asList("unitPrice",  "originalOperationFee");

                    // 移除指定名称的字段
                    fields = removeFieldsByName(fields, removeFieldNames);

                    if(nowColumn>=fields.length){
                        field = fields[fields.length-2];
                    }else{
                        field = fields[nowColumn];
                    }

                    ExcelFieldAnnotation annotation = field.getAnnotation(ExcelFieldAnnotation.class);

                    Cell titleCell = titleRow.getCell(nowColumn);
                    String column = "";
                    if (titleCell != null) {
                        column = titleCell.getStringCellValue();
                    }

                    boolean required = false;
                    if (annotation != null) {
                        required = annotation.required();
                    }

                    Class<?> fieldType = field.getType();
                    try {
                        Object cellValue = getCellValueByType(fieldType, cell);
                        if (required && ObjectUtil.isEmpty(cellValue)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, column));
                        }

                        if (nowColumn == startCol) {
                            // 一次性把数据放完整 一列的 所有金额相关
//                          4个属性填充 为一个jsonObject
                            for (int i = nowColumn; i < titleRow.getLastCellNum(); i++) {
                                //
                                if(i == startCol || (i - startCol) % fieldSpacing == 0){
                                    JSONObject json = new JSONObject();
                                    for (int z = 0; z < 4; z++) {
                                        if(i==startCol||i==startCol+fieldSpacing){
                                            log.info("i = {}", i);
                                        }                                        // 遍历 其实还需要存等级
                                        Field fieldPrice = fields[nowColumn];
                                        Class<?> fieldType1 = fieldPrice.getType();
                                        Cell cellPrice = excelReader.getCell(i+z, nowRow);
                                        Object price = getCellValueByType(fieldType1, cellPrice);
                                        if (z == 0) {
                                            json.put("unitPrice",price);
                                        }
                                        if (z == 1) {
                                            json.put("operationFee", price);
                                        }
                                        if (z == 2) {
                                            json.put("finalDeliveryFee", price);
                                        }
                                        if (z == 3) {
                                            Object levelName = cellMap.get(i);
                                            if (ObjectUtil.isNotEmpty(levelName)){
                                                json.put("levelName", levelName);
                                            }else {
                                                throw new Exception("等级名称不能为空");
                                            }

                                        }

                                    }
                                    extendedColumn.add(json);
                                }

                            }
                        }
                        // priceJSON 和itemNo 实际只有两列,列类型只有两列,所以下面的rowIndex 和 shouRowIndex会不断的覆盖 在priceJSON执行完了以后应该直接continue;
                        if("priceJSON".equals(field.getName())){
                            ReflectUtil.setFieldValue(newInstance, field, extendedColumn);
                        }else{
                            // 存在下标丢失的情况rowIndex 和showRowIndex
                            if(ObjectUtil.isNotEmpty(cellValue)){
                                ReflectUtil.setFieldValue(newInstance, field, cellValue);
                            }
                        }
                    } catch (ExcelMessageException e) {
                        throw e;
                    } catch (IllegalArgumentException e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    } catch (Exception e) {
                        throw new ExcelMessageException(ExcelMessageEnum.ILLEGAL_ARGUMENT.buildLocalMessage(showRowIndex, column));
                    }
                }
                list.add(newInstance);
            } catch (ExcelMessageException e) {
                // 异常源头
                log.error("parseFieldDTO ExcelStatusCodeException {}", e.getMessage(), e);
                localeMessage.appendSurround(e.getLocaleMessage(), "<p>", "</p></br>");
            } catch (Exception e) {
                log.error("parseFieldDTO error {}", e.getMessage(), e);
            }
        }

        if (localeMessage.hasData()) {
            throw new ExcelMessageException(localeMessage);
        }
        return list;
    }

    public static <T extends ExcelBaseDTO> ExcelMsgBuilder<T> msgBuilder(ExcelReader excelReader, int titleRowIdx,
                                                                         Class<T> beanClass) {
        return new ExcelMsgBuilder<>(excelReader.getOrCreateRow(titleRowIdx), beanClass);
    }

    private static Object getCellValueByType(Class<?> fieldType, Cell cell) {
        Object value = null;
        if (cell != null) {
            Object cellValue = CellUtil.getCellValue(cell);
            if (cellValue != null && StrUtil.isNotBlank(cellValue.toString())) {
                try {
                    if (fieldType == Double.class) {
                        value = NumberUtil.parseDouble(cellValue.toString());
                    } else if (fieldType == Integer.class) {
                        value = NumberUtil.parseInt(cellValue.toString());
                    } else if (fieldType == BigDecimal.class) {
                        value = NumberUtil.toBigDecimal(cellValue.toString());
                    } else {
                        value = StrUtil.trim(cellValue.toString());
                    }
                } catch (IllegalArgumentException e) {
                    throw e;
                }
            }
        }
        return value;
    }

}
