#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    import PyPDF2
    print("Using PyPDF2")
    
    with open('1.pdf', 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        print(f"PDF has {len(reader.pages)} pages")
        
        # Extract text from all pages
        full_text = ""
        for i, page in enumerate(reader.pages):
            text = page.extract_text()
            full_text += f"\n--- Page {i+1} ---\n"
            full_text += text
            
        print("PDF Content:")
        print(full_text)
        
except ImportError:
    print("PyPDF2 not available, trying pdfplumber")
    try:
        import pdfplumber
        
        with pdfplumber.open('1.pdf') as pdf:
            print(f"PDF has {len(pdf.pages)} pages")
            
            full_text = ""
            for i, page in enumerate(pdf.pages):
                text = page.extract_text()
                full_text += f"\n--- Page {i+1} ---\n"
                full_text += text if text else "[No text found]"
                
            print("PDF Content:")
            print(full_text)
            
    except ImportError:
        print("Neither PyPDF2 nor pdfplumber available")
        print("Trying basic file reading...")
        
        # Try to read as text (might not work for binary PDF)
        try:
            with open('1.pdf', 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                print("Raw content (first 2000 chars):")
                print(content[:2000])
        except Exception as e:
            print(f"Error reading file: {e}")
