package com.hengjian.common.translation.core.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.service.UserService;
import com.hengjian.common.translation.annotation.TranslationType;
import com.hengjian.common.translation.constant.TransConstant;
import com.hengjian.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * 用户名翻译实现
 *
 * <AUTHOR> Li
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.NOT_TENANT_USER_ID_TO_NAME)
public class NotTenantUserNameTranslationImpl implements TranslationInterface<String> {

    private final UserService userService;

    @Override
    public String translation(Object key, String other) {
//        Console.log("NotTenantUserNameTranslationImpl.........start");
        if (key instanceof Long) {
            String userName = userService.selectUserNameByIdNoTenantNotCache((Long) key);
            if(StrUtil.equals("Sensitive", other)) {
                userName = DesensitizedUtil.idCardNum(userName, 1, 1);
            }
            return userName;
        }
        return null;
    }
}
