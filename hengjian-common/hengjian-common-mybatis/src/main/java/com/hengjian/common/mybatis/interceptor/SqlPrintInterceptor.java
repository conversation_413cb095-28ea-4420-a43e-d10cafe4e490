package com.hengjian.common.mybatis.interceptor;

/**
 * 功能描述：此拦截器是为了修复站点改造后,原生mybatis的日志输出内针对$的字符拼接报错.
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
//@Intercepts
//    ({
//        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
//        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
//    })
//@Slf4j
//public class SqlPrintInterceptor implements Interceptor {
//
//    private static Log logger = LogFactory.getLog(SqlPrintInterceptor.class);
//
//    private static final DateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
//        Object parameterObject = null;
//        if (invocation.getArgs().length > 1) {
//            parameterObject = invocation.getArgs()[1];
//        }
//
//        long start = System.currentTimeMillis();
//        Object result = null;
//        try {
//            result = invocation.proceed();
//        } catch (IllegalArgumentException e) {
//            log.error("log日志异常,字符串$符号拼接异常", e);
//        }
//
//
//        String statementId = mappedStatement.getId();
//        BoundSql boundSql = mappedStatement.getBoundSql(parameterObject);
//        Configuration configuration = mappedStatement.getConfiguration();
//        String sql = getSql(boundSql, parameterObject, configuration);
//
//        long end = System.currentTimeMillis();
//        long timing = end - start;
//        if(logger.isInfoEnabled()){
//            logger.info("执行sql耗时:" + timing + " ms" + " - id:" + statementId + " - Sql:" );
//            logger.info("运行sql原文"+sql);
//        }
//
//        return result;
//    }
//
//    @Override
//    public Object plugin(Object target) {
//        if (target instanceof Executor) {
//            return Plugin.wrap(target, this);
//        }
//        return target;
//    }
//
//    @Override
//    public void setProperties(Properties properties) {
//    }
//
//    private String getSql(BoundSql boundSql, Object parameterObject, Configuration configuration) {
//        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
//        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
//        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
//        if (parameterMappings != null) {
//            for (int i = 0; i < parameterMappings.size(); i++) {
//                ParameterMapping parameterMapping = parameterMappings.get(i);
//                if (parameterMapping.getMode() != ParameterMode.OUT) {
//                    Object value;
//                    String propertyName = parameterMapping.getProperty();
//                    if (boundSql.hasAdditionalParameter(propertyName)) {
//                        value = boundSql.getAdditionalParameter(propertyName);
//                    } else if (parameterObject == null) {
//                        value = null;
//                    } else if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
//                        value = parameterObject;
//                    } else {
//                        MetaObject metaObject = configuration.newMetaObject(parameterObject);
//                        value = metaObject.getValue(propertyName);
//                    }
//                    sql = replacePlaceholder(sql, value);
//                }
//            }
//        }
//        return sql;
//    }
//
//    private String replacePlaceholder(String sql, Object propertyValue) {
//        String result;
//        if (propertyValue != null) {
//            if (propertyValue instanceof String) {
//                result = "'" + propertyValue + "'";
//            } else if (propertyValue instanceof Date) {
//                result = "'" + DATE_FORMAT.format(propertyValue) + "'";
//            } else {
//                result = propertyValue.toString();
//            }
//        } else {
//            result = "null";
//        }
//        return sql.replaceFirst("\\?", Matcher.quoteReplacement(result));
//    }
//}
